之前的插件太过无用
    只需要设计出utils
        protocol既是suject也是observer
        上层net层也会既是subject也是observer
        收到消息net sengmessage 到protocol，protocol收到后发送给104 convertobj然后根据不同的cot选择不同的asdu，像pro文件夹那样
        还要有个专门的类在protocol下专门管理各种东西，叫什么比较好？
            这个类要管理所有的消息，根据invokeid 去一一管理消息，因为这里可能有很多帧？这个可以参考prooperation
            还要要管理的    第一个convert，这个会生成本地定义的消息类型
                        第二个类，这个类专门线程池去执行命令，接收convert发过来的消息，如果是查数据库什么的可以直接在线程池本地操作，如果是event那就广播，command那就专门发送，等待结果

        plugin接收消息网络上接受的是不同的消息，这里处理的都是相同规范的，所以不同的操作实现一次就可以了


链路状态变化回调 - SetLinkStatusChgCalBak()     tcpserver管理了，他会在连接断开后自动析构gateway的

心跳，需要的，这个客户端也要支持的吧，先不搞

启动协议业务
停止协议业务
链路状态管理不需要，有更上层在管理了
协议特定功能    帧的管理，这咋搞？
日志就这样 足够了
配置    后面搞，先不弄
统计和监控足够了


网络层 通过mediator发送消息，   现在有三个队列，应该在observer的回调里面管理是common 还是event加入不同的队列
协议命令处理线程  从common队列中取消息  通过transform转换后向service发送消息 
召唤响应处理线程  从response队列中取消息    从service接收消息转换成103规范再发到网络层
事件通知处理线程  从event队列中取消息     从service接收消息转换成103规范再发到网络层，上层不能发送event到下层去
召唤请求处理线程  处理超时请求和请求清理




Service 保证响应顺序：Service 按照接收到的命令顺序返回结果
invoke_id 只用于匹配：格式 包ID/序号，序号只是标识，不用于排序
Gateway 不需要排序：直接按接收顺序 push_back，然后按顺序输出
最后一帧标识：通过计数器判断是否为最后一帧，设置 b_lastmsg = true


现在是接受的时候判断他的类型是否需要多帧，但是没有办法判断接受的时候是不是最后一帧啊，然后就是要修改service，不要那么多没用的代码，要支持多帧上送才行，然后gateway也要修改，以支持多帧转换，，这样的话transform也要修改，原来的代码是什么样子的？proopeartion每次只会转换一整个体？那这样的话ProtocolFrame的bool is_last_frame = true;  // 是否最后一帧（用于多帧合并）还有意义吗？



5.1 添加 b_lastmsg 字段
5.2 修改 Transform 接口
支持 ProtocolFrameList 输入
一次性转换整个多帧请求
5.3 修改 Gateway 逻辑
等到 is_last_frame = true 才进行转换
支持多帧合并
5.4 修改 Service 逻辑
支持多帧上送
设置 b_lastmsg 标识
6. 关键结论
is_last_frame 字段非常有意义 - 它是协议层面的关键信息
必须从协议数据中解析 - 不能由 Gateway 随意设置
ProOperation 确实是等到完整后一次性转换 - 这是正确的设计
需要全面修改架构 - Gateway、Transform、Service 都需要支持多帧处理


我修改了gw104的transform，现在type 1为单帧测试，type2为多帧测试，现在的转换还是这样的，直接把proframe的data放到common里面就行，转换后，service，返回两帧同样的common帧，第一个common不能是最后一帧，要等两帧一起发送   




gateway中接收多帧这里是有问题的，没办法确定某一帧是不是最后一帧

