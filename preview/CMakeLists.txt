cmake_minimum_required(VERSION 3.15)
project(zexuan VERSION 1.0.0)

# 使用 ccache 加速编译
set_property(GLOBAL PROPERTY RULE_LAUNCH_COMPILE ccache)
set_property(GLOBAL PROPERTY RULE_LAUNCH_LINK ccache)

# 设置 C++ 标准为 C++23 以支持 std::expected 等现代化特性
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

find_package(GTest REQUIRED)

# 添加子项目
add_subdirectory(core)
# add_subdirectory(app)
add_subdirectory(test)
add_subdirectory(protocol)