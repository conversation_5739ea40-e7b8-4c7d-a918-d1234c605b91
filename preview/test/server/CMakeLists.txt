

# 协议测试服务器
add_executable(server src/main.cpp)
target_link_libraries(server
                        core
                        protocol_server
                        protocol_gateway
                        protocol_service
                        protocol_transform
    )
target_include_directories(server PRIVATE
    include
)

set_target_properties(server PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/test"
)