#include "spdlog/spdlog.h"
#include <mutex>
#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/tcp_server.hpp"

#include <set>
#include <stdio.h>
#include <unistd.h>
#include <signal.h>
#include "core.hpp"

using namespace zexuan;
using namespace zexuan::net;



class ChatServer
{
 public:
  ChatServer(EventLoop* loop,
             const zexuan::platform::network::Address& listenAddr)
  : server_(loop, listenAddr, "ChatServer")
  {
    server_.setConnectionCallback(
        std::bind(&ChatServer::onConnection, this, std::placeholders::_1));
    server_.setMessageCallback(
        std::bind(&ChatServer::onMessage, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
  }

  void setThreadNum(int numThreads)
  {
    server_.setThreadNum(numThreads);
  }

  void start()
  {
    server_.start();
  }

 private:
  void onConnection(const TcpConnectionPtr& conn)
  {
    spdlog::info("{} -> {} is {}", 
                 conn->peerAddress().toIpPort(),
                 conn->localAddress().toIpPort(),
                 conn->connected() ? "UP" : "DOWN");

    std::lock_guard<std::mutex> lock(mutex_);
    if (conn->connected())
    {
      connections_.insert(conn);
    }
    else
    {
      connections_.erase(conn);
    }
  }

  void onMessage(const TcpConnectionPtr& conn,
                 Buffer* buf,
                 std::chrono::system_clock::time_point)
  {
    while (buf->readableBytes() > 0)
    {
      std::string message(buf->peek(), buf->readableBytes());
      buf->retrieveAll();
      
      std::lock_guard<std::mutex> lock(mutex_);
      for (auto& connection : connections_)
      {
        get_pointer(connection)->send(message);
      }
    }
  }

  typedef std::set<TcpConnectionPtr> ConnectionList;
  TcpServer server_;
  std::mutex mutex_;
  ConnectionList connections_;
};

int main(int argc, char* argv[])
{
  spdlog::info("pid = {}", getpid());
  initialize_core_library();
  if (argc > 1)
  {
    EventLoop loop;
    
    uint16_t port = static_cast<uint16_t>(atoi(argv[1]));
    zexuan::platform::network::Address serverAddr(port);
    ChatServer server(&loop, serverAddr);
    if (argc > 2)
    {
      server.setThreadNum(atoi(argv[2]));
    }
    server.start();
    spdlog::info("ChatServer启动成功，按Ctrl+C退出");
    loop.loop();
  }
  else
  {
    spdlog::warn("Usage: {} port [thread_num]", argv[0]);
    printf("Usage: %s port [thread_num]\n", argv[0]);
  }
}

