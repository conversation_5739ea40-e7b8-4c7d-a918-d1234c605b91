#include <gtest/gtest.h>
#include "zexuan/platform/network/address.hpp"
#include "zexuan/platform/network/sockets_ops.hpp"
#include "zexuan/platform/network/socket.hpp"
#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <cstring>
#include <arpa/inet.h>
#include <netinet/tcp.h>

using namespace zexuan::platform::network;

// Address 类测试
class AddressTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 测试前的设置
    }
    
    void TearDown() override {
        // 测试后的清理
    }
};

TEST_F(AddressTest, DefaultConstructor) {
    Address addr;
    EXPECT_EQ(addr.port(), 0);
    EXPECT_EQ(addr.family(), AF_INET);
    EXPECT_EQ(addr.toIp(), "0.0.0.0");
    EXPECT_EQ(addr.toIpPort(), "0.0.0.0:0");
}

TEST_F(AddressTest, PortConstructor) {
    Address addr(8080);
    EXPECT_EQ(addr.port(), 8080);
    EXPECT_EQ(addr.family(), AF_INET);
    EXPECT_EQ(addr.toIp(), "0.0.0.0");
    EXPECT_EQ(addr.toIpPort(), "0.0.0.0:8080");
}

TEST_F(AddressTest, PortConstructorLoopback) {
    Address addr(9090, true);
    EXPECT_EQ(addr.port(), 9090);
    EXPECT_EQ(addr.family(), AF_INET);
    EXPECT_EQ(addr.toIp(), "127.0.0.1");
    EXPECT_EQ(addr.toIpPort(), "127.0.0.1:9090");
}

TEST_F(AddressTest, PortConstructorIPv6) {
    Address addr(9090, false, true);
    EXPECT_EQ(addr.port(), 9090);
    EXPECT_EQ(addr.family(), AF_INET6);
    EXPECT_EQ(addr.toIp(), "::");
    EXPECT_EQ(addr.toIpPort(), "[::]:9090");
}

TEST_F(AddressTest, IpPortConstructor) {
    Address addr("***********", 80);
    EXPECT_EQ(addr.port(), 80);
    EXPECT_EQ(addr.family(), AF_INET);
    EXPECT_EQ(addr.toIp(), "***********");
    EXPECT_EQ(addr.toIpPort(), "***********:80");
}

TEST_F(AddressTest, IpPortConstructorIPv6) {
    Address addr("::1", 80, true);
    EXPECT_EQ(addr.port(), 80);
    EXPECT_EQ(addr.family(), AF_INET6);
    EXPECT_EQ(addr.toIp(), "::1");
    EXPECT_EQ(addr.toIpPort(), "[::1]:80");
}

TEST_F(AddressTest, SockAddrConstructor) {
    struct sockaddr_in sockaddr;
    std::memset(&sockaddr, 0, sizeof(sockaddr));
    sockaddr.sin_family = AF_INET;
    sockaddr.sin_port = htons(1234);
    inet_pton(AF_INET, "********", &sockaddr.sin_addr);
    
    Address addr(sockaddr);
    EXPECT_EQ(addr.port(), 1234);
    EXPECT_EQ(addr.family(), AF_INET);
    EXPECT_EQ(addr.toIp(), "********");
    EXPECT_EQ(addr.toIpPort(), "********:1234");
}

TEST_F(AddressTest, StaticFactoryMethods) {
    Address addr1 = Address::fromIpPort("**********", 443);
    EXPECT_EQ(addr1.port(), 443);
    EXPECT_EQ(addr1.toIp(), "**********");
    
    struct sockaddr_in sockaddr;
    std::memset(&sockaddr, 0, sizeof(sockaddr));
    sockaddr.sin_family = AF_INET;
    sockaddr.sin_port = htons(8080);
    inet_pton(AF_INET, "127.0.0.1", &sockaddr.sin_addr);
    
    Address addr2 = Address::fromSockAddr(*reinterpret_cast<struct sockaddr*>(&sockaddr));
    EXPECT_EQ(addr2.port(), 8080);
    EXPECT_EQ(addr2.toIp(), "127.0.0.1");
}

TEST_F(AddressTest, NetworkEndian) {
    Address addr("***********00", 12345);
    EXPECT_EQ(addr.portNetEndian(), htons(12345));
    
    // 使用 inet_pton 来确保一致性
    struct in_addr expected_addr;
    inet_pton(AF_INET, "***********00", &expected_addr);
    EXPECT_EQ(addr.ipv4NetEndian(), expected_addr.s_addr);
}

TEST_F(AddressTest, Resolve) {
    Address result;
    // 测试本地主机名解析
    bool resolved = Address::resolve("localhost", &result);
    if (resolved) {
        EXPECT_EQ(result.family(), AF_INET);
        EXPECT_EQ(result.toIp(), "127.0.0.1");
    }
}

TEST_F(AddressTest, SetScopeId) {
    Address addr(8080, false, true); // IPv6
    addr.setScopeId(1);
    // 验证作用域 ID 已设置（内部实现细节）
}

// sockets_ops 命名空间测试
class SocketsOpsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 测试前的设置
    }
    
    void TearDown() override {
        // 测试后的清理
    }
};

TEST_F(SocketsOpsTest, AddressConversion) {
    struct sockaddr_in addr;
    std::memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_port = htons(8080);
    inet_pton(AF_INET, "***********", &addr.sin_addr);
    
    char ip_buf[INET_ADDRSTRLEN];
    sockets::toIp(ip_buf, sizeof(ip_buf), reinterpret_cast<struct sockaddr*>(&addr));
    EXPECT_STREQ(ip_buf, "***********");
    
    char ip_port_buf[64];
    sockets::toIpPort(ip_port_buf, sizeof(ip_port_buf), reinterpret_cast<struct sockaddr*>(&addr));
    EXPECT_STREQ(ip_port_buf, "***********:8080");
}

TEST_F(SocketsOpsTest, FromIpPort) {
    struct sockaddr_in addr;
    std::memset(&addr, 0, sizeof(addr));
    
    sockets::fromIpPort("********", 1234, &addr);
    EXPECT_EQ(addr.sin_family, AF_INET);
    EXPECT_EQ(ntohs(addr.sin_port), 1234);
    
    char ip_buf[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &addr.sin_addr, ip_buf, sizeof(ip_buf));
    EXPECT_STREQ(ip_buf, "********");
}

TEST_F(SocketsOpsTest, AddressCasting) {
    struct sockaddr_in addr4;
    struct sockaddr_in6 addr6;
    std::memset(&addr4, 0, sizeof(addr4));
    std::memset(&addr6, 0, sizeof(addr6));
    
    addr4.sin_family = AF_INET;
    addr6.sin6_family = AF_INET6;
    
    const struct sockaddr* sockaddr4 = sockets::sockaddr_cast(&addr4);
    const struct sockaddr* sockaddr6 = sockets::sockaddr_cast(&addr6);
    
    EXPECT_EQ(sockaddr4->sa_family, AF_INET);
    EXPECT_EQ(sockaddr6->sa_family, AF_INET6);
    
    const struct sockaddr_in* casted4 = sockets::sockaddr_in_cast(sockaddr4);
    const struct sockaddr_in6* casted6 = sockets::sockaddr_in6_cast(sockaddr6);
    
    EXPECT_EQ(casted4->sin_family, AF_INET);
    EXPECT_EQ(casted6->sin6_family, AF_INET6);
}

// Socket 类测试
class SocketTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建测试套接字
        sockfd_ = sockets::createNonblockingOrDie(AF_INET);
        ASSERT_GE(sockfd_, 0) << "Failed to create socket";
    }
    
    void TearDown() override {
        // 清理套接字
        if (sockfd_ >= 0) {
            sockets::close(sockfd_);
        }
    }
    
    int sockfd_;
};

TEST_F(SocketTest, Constructor) {
    Socket socket(sockfd_);
    EXPECT_EQ(socket.fd(), sockfd_);
}

TEST_F(SocketTest, Destructor) {
    // 创建一个新的套接字用于测试析构
    int test_sockfd = sockets::createNonblockingOrDie(AF_INET);
    ASSERT_GE(test_sockfd, 0);
    
    {
        Socket socket(test_sockfd);
        EXPECT_EQ(socket.fd(), test_sockfd);
    } // 析构函数会自动关闭套接字
}

TEST_F(SocketTest, SocketOptions) {
    Socket socket(sockfd_);
    
    // 测试各种套接字选项
    EXPECT_NO_THROW(socket.setTcpNoDelay(true));
    EXPECT_NO_THROW(socket.setReuseAddr(true));
    EXPECT_NO_THROW(socket.setReusePort(true));
    EXPECT_NO_THROW(socket.setKeepAlive(true));
}

TEST_F(SocketTest, TcpInfo) {
    Socket socket(sockfd_);
    
    // 测试 TCP 信息获取
    struct tcp_info tcpi;
    bool has_tcp_info = socket.getTcpInfo(&tcpi);
    
    // 对于新创建的套接字，可能无法获取 TCP 信息
    // 这取决于操作系统和套接字状态
    if (has_tcp_info) {
        EXPECT_GE(tcpi.tcpi_rtt, 0);
        EXPECT_GE(tcpi.tcpi_snd_mss, 0);
        EXPECT_GE(tcpi.tcpi_rcv_mss, 0);
    }
    
    // 测试 TCP 信息字符串
    char tcp_info_buf[1024];
    bool has_tcp_info_str = socket.getTcpInfoString(tcp_info_buf, sizeof(tcp_info_buf));
    
    if (has_tcp_info_str) {
        EXPECT_GT(strlen(tcp_info_buf), 0);
        std::cout << "TCP Info: " << tcp_info_buf << std::endl;
    }
}

TEST_F(SocketTest, BindAndListen) {
    Socket socket(sockfd_);
    
    // 绑定地址
    Address addr(0); // 使用端口 0，让系统分配
    EXPECT_NO_THROW(socket.bindAddress(addr));
    
    // 开始监听
    EXPECT_NO_THROW(socket.listen());
}

TEST_F(SocketTest, ShutdownWrite) {
    Socket socket(sockfd_);
    EXPECT_NO_THROW(socket.shutdownWrite());
}

// 集成测试
class IntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置测试环境
    }
    
    void TearDown() override {
        // 清理测试环境
    }
};

TEST_F(IntegrationTest, AddressAndSocketIntegration) {
    // 创建地址
    Address addr("127.0.0.1", 0);
    EXPECT_EQ(addr.toIp(), "127.0.0.1");
    
    // 创建套接字
    int sockfd = sockets::createNonblockingOrDie(AF_INET);
    ASSERT_GE(sockfd, 0);
    
    Socket socket(sockfd);
    
    // 绑定地址
    EXPECT_NO_THROW(socket.bindAddress(addr));
    
    // 开始监听
    EXPECT_NO_THROW(socket.listen());
    
    // 清理
    // Socket 析构函数会自动关闭套接字
}

TEST_F(IntegrationTest, MultipleAddresses) {
    std::vector<std::pair<std::string, uint16_t>> test_addresses = {
        {"127.0.0.1", 8080},
        {"0.0.0.0", 9090},
        {"***********", 80},
        {"********", 443}
    };
    
    for (const auto& [ip, port] : test_addresses) {
        Address addr(ip, port);
        EXPECT_EQ(addr.toIp(), ip);
        EXPECT_EQ(addr.port(), port);
        EXPECT_EQ(addr.toIpPort(), ip + ":" + std::to_string(port));
    }
}

// 性能测试
TEST(PerformanceTest, AddressCreation) {
    const int iterations = 10000;
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < iterations; ++i) {
        Address addr("***********00", i % 65536);
        (void)addr.toIpPort(); // 避免编译器优化
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "Created " << iterations << " addresses in " << duration.count() << " microseconds" << std::endl;
}

// 边界情况测试
class EdgeCaseTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置测试环境
    }
    
    void TearDown() override {
        // 清理测试环境
    }
};

TEST_F(EdgeCaseTest, InvalidAddresses) {
    // 测试无效的 IP 地址
    Address addr("invalid.ip.address", 8080);
    // 应该使用默认地址或抛出异常
    EXPECT_EQ(addr.toIp(), "0.0.0.0");
}

TEST_F(EdgeCaseTest, PortBoundaries) {
    // 测试端口边界值
    Address addr1("127.0.0.1", 0);
    EXPECT_EQ(addr1.port(), 0);
    
    Address addr2("127.0.0.1", 65535);
    EXPECT_EQ(addr2.port(), 65535);
}

int main(int argc, char** argv) {
    std::cout << "开始测试平台网络模块..." << std::endl;
    
    ::testing::InitGoogleTest(&argc, argv);
    int result = RUN_ALL_TESTS();
    
    std::cout << "平台网络模块测试完成！" << std::endl;
    return result;
}