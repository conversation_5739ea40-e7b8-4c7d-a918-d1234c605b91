#include <gtest/gtest.h>
#include "zexuan/platform/network/poller.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/channel.hpp"

using namespace zexuan::platform::network;
using namespace zexuan::net;

// 简单的 Poller 测试
class PollerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置测试环境
    }
    
    void TearDown() override {
        // 清理测试环境
    }
};

TEST_F(PollerTest, BasicFunctionality) {
    // 测试 Poller 的基本功能
    // 由于 Poller 是抽象类，这里主要测试接口定义
    
    // 验证 Poller 类可以正确包含
    EXPECT_TRUE(true);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
