#ifndef ZEXUAN_NET_CALLBACKS_HPP
#define ZEXUAN_NET_CALLBACKS_HPP

#include <functional>
#include <memory>
#include <type_traits>
#include <chrono>

namespace zexuan {
namespace net {
class Channel;
class Buffer;
class TcpConnection;
class EventLoop;

template<typename T>
inline T* get_pointer(const std::shared_ptr<T>& ptr) {
    return ptr.get();
}

template<typename T>
inline T* get_pointer(const std::unique_ptr<T>& ptr) {
    return ptr.get();
}

// All client visible callbacks go here.


using TcpConnectionPtr = std::shared_ptr<TcpConnection>;
using ConnectionCallback = std::function<void (const TcpConnectionPtr&)>;
using CloseCallback = std::function<void (const TcpConnectionPtr&)>;
using WriteCompleteCallback = std::function<void (const TcpConnectionPtr&)>;
using HighWaterMarkCallback = std::function<void (const TcpConnectionPtr&, size_t)>;
using ChannelList = std::vector<Channel*>;
using Timestamp = std::chrono::system_clock::time_point;
using MessageCallback = std::function<void (const TcpConnectionPtr&,
                                            Buffer*,
                                            Timestamp)>;
using EventCallback = std::function<void()>;
using ReadEventCallback = std::function<void(Timestamp)>;
using NewConnectionCallback = std::function<void (int sockfd)>;
using ThreadInitCallback = std::function<void(EventLoop*)>;
using Functor = std::function<void()>;
// the data has been read to (buf, len)


void defaultConnectionCallback(const TcpConnectionPtr& conn);
void defaultMessageCallback(const TcpConnectionPtr& conn,
                            Buffer* buffer,
                            std::chrono::system_clock::time_point receiveTime);

} // namespace net
} // namespace zexuan

#endif // ZEXUAN_NET_CALLBACKS_HPP
