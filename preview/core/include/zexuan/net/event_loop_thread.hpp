#ifndef ZEXUAN_NET_EVENT_LOOP_THREAD_HPP
#define ZEXUAN_NET_EVENT_LOOP_THREAD_HPP

#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <functional>
#include "zexuan/net/callbacks.hpp"

namespace zexuan {
namespace net {

class EventLoop;

class EventLoopThread {
public:

    EventLoopThread(const ThreadInitCallback& cb = ThreadInitCallback(),
                    const std::string& name = std::string());
    ~EventLoopThread();

    // 禁用拷贝构造和赋值
    EventLoopThread(const EventLoopThread&) = delete;
    EventLoopThread& operator=(const EventLoopThread&) = delete;

    EventLoop* startLoop();

private:
    void threadFunc();

    EventLoop* loop_;
    std::atomic<bool> exiting_;
    std::thread thread_;
    std::mutex mutex_;
    std::condition_variable cond_;
    ThreadInitCallback callback_;
    std::string name_;  // 保留名称用于调试
};

} // namespace net
} // namespace zexuan

#endif // ZEXUAN_NET_EVENT_LOOP_THREAD_HPP
