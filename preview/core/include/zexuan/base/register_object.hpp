#ifndef ZEXUAN_BASE_REGISTER_OBJECT_H
#define ZEXUAN_BASE_REGISTER_OBJECT_H

#include "base_types.hpp"
#include <memory>
#include <stdexcept>
#include <atomic>

namespace zexuan {
namespace base {

// 前向声明
class Mediator;

/**
 * @brief 注册对象基类
 *
 * 封装目标者和观察者注册管理的公共操作
 * 使用现代 C++ 特性实现 RAII 和异常安全
 * 重构后提供更好的代码复用和接口隔离
 */
class RegisterObject : public std::enable_shared_from_this<RegisterObject>,
                      public IRegisterable {
public:
    /**
     * @brief 构造函数
     * @param object_id 对象唯一标识
     * @param class_name 类名
     */
    explicit RegisterObject(ObjectId object_id, std::string class_name);

    /**
     * @brief 虚析构函数
     */
    virtual ~RegisterObject() noexcept;

    /**
     * @brief 判断对于参数指定的事件类型和设备是否关注
     *
     * 这是一个模板方法，派生类需要实现具体的关注逻辑
     * 基类提供了通用的辅助方法来支持实现
     *
     * @param event_type 事件类型
     * @param device_id 设备标识
     * @return true:关注 false:不关注
     */
    virtual bool IsCare(EventType event_type, const DeviceUUID& device_id) const noexcept = 0;

    /**
     * @brief 设置注册对象描述信息
     * @param description 描述信息
     * @return 操作结果
     */
    virtual VoidResult SetRegObjDesc(std::string description) noexcept;

    /**
     * @brief 获取注册对象属性信息
     * @return 注册对象结构信息的常量引用
     */
    virtual const RegisterObjectInfo& GetRegObjProperty() const noexcept;

    /**
     * @brief 初始化(注册到服务中介等操作)
     * @return 操作结果
     */
    virtual VoidResult Init();

    /**
     * @brief 退出(从服务中介注销等操作)
     * @return 操作结果
     */
    virtual VoidResult Exit() noexcept;

    /**
     * @brief 获取对象ID (实现IRegisterable接口)
     */
    ObjectId GetObjectId() const noexcept override { return reg_obj_.object_id; }

    /**
     * @brief 获取类名
     */
    const std::string& GetClassName() const noexcept { return class_name_; }

protected:
    /**
     * @brief 将自身注册到服务中介 (实现IRegisterable接口)
     *
     * 这是一个模板方法，提供通用的注册流程
     * 派生类可以重写以实现特定的注册逻辑
     *
     * @return 操作结果
     */
    VoidResult RegisterToMediator() override;

    /**
     * @brief 将自身从服务中介注销 (实现IRegisterable接口)
     *
     * 这是一个模板方法，提供通用的注销流程
     * 派生类可以重写以实现特定的注销逻辑
     *
     * @return 操作结果
     */
    VoidResult CancelFromMediator() noexcept override;

    /**
     * @brief 判断对于参数指定的事件类型是否关注
     * @param event_type 事件类型
     * @return true:关注 false:不关注
     */
    virtual bool IsCareEventInfo(EventType event_type) const noexcept;

    /**
     * @brief 判断对于参数指定的设备ID是否关注
     * @param device_id 设备ID
     * @return true:关注 false:不关注
     */
    virtual bool IsCareDevInfo(const DeviceUUID& device_id) const noexcept;

    /**
     * @brief 设置中介者指针
     */
    void SetMediator(std::shared_ptr<Mediator> mediator) noexcept;

    /**
     * @brief 获取中介者指针
     */
    std::shared_ptr<Mediator> GetMediator() const noexcept;

    /**
     * @brief 派生类特定的注册逻辑钩子方法
     *
     * 派生类可以重写此方法来实现特定的注册逻辑
     * 在通用注册流程中被调用
     *
     * @return 操作结果
     */
    virtual VoidResult DoRegister() { return {}; }

    /**
     * @brief 派生类特定的注销逻辑钩子方法
     *
     * 派生类可以重写此方法来实现特定的注销逻辑
     * 在通用注销流程中被调用
     *
     * @return 操作结果
     */
    virtual VoidResult DoUnregister() noexcept { return {}; }

protected:
    /** @brief 注册对象信息 */
    RegisterObjectInfo reg_obj_;

    /** @brief 服务中介对象 */
    std::shared_ptr<Mediator> mediator_;

    /** @brief 类名 */
    std::string class_name_;

protected:
    /** @brief 保护对象状态的互斥锁 (供派生类使用) */
    mutable std::mutex state_mutex_;

private:
    /** @brief 初始化状态 */
    std::atomic<bool> initialized_{false};
};

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_REGISTER_OBJECT_H
