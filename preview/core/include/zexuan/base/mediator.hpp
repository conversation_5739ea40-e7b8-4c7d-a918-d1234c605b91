#ifndef ZEXUAN_BASE_MEDIATOR_H
#define ZEXUAN_BASE_MEDIATOR_H

#include "base_types.hpp"
#include <unordered_map>
#include <mutex>
#include <shared_mutex>

namespace zexuan {
namespace base {

// 前向声明
class Subject;
class Observer;

/**
 * @brief 服务中介类
 *
 * 实现对"观察者"和"目标者"行为的封装，使得两者相互解耦并实现通信
 * 使用现代 C++ 特性实现线程安全
 */
class Mediator {
public:
    /**
     * @brief 构造函数
     */
    Mediator();

    /**
     * @brief 析构函数
     */
    ~Mediator() noexcept;

    // 禁用拷贝和移动
    Mediator(const Mediator&) = delete;
    Mediator& operator=(const Mediator&) = delete;
    Mediator(Mediator&&) = delete;
    Mediator& operator=(Mediator&&) = delete;

    /**
     * @brief 注册观察者
     * @param observer_id 观察者ID
     * @param observer 观察者对象智能指针
     * @return 操作结果
     */
    VoidResult EnrollObserver(ObjectId observer_id, std::shared_ptr<Observer> observer);

    /**
     * @brief 注销观察者
     * @param observer_id 观察者对象唯一标识
     * @return 操作结果
     */
    VoidResult CancelObserver(ObjectId observer_id) noexcept;

    /**
     * @brief 注册目标者
     * @param subject_id 目标者对象唯一标识
     * @param subject 目标者对象智能指针
     * @return 操作结果
     */
    VoidResult EnrollSubject(ObjectId subject_id, std::shared_ptr<Subject> subject);

    /**
     * @brief 注销目标者
     * @param subject_id 目标者对象唯一标识
     * @return 操作结果
     */
    VoidResult CancelSubject(ObjectId subject_id) noexcept;

    /**
     * @brief 向目标者发送通用消息
     * @param subject_id 目标者ID
     * @param msg 要发送的通用消息
     * @param source_id 消息源对象ID
     * @return 操作结果
     */
    VoidResult SendCommonMsgToSubject(ObjectId subject_id, const CommonMessage& msg, ObjectId source_id);

    /**
     * @brief 向观察者发送通用消息
     * @param observer_id 观察者ID
     * @param msg 要发送的通用消息
     * @param source_id 消息源对象ID
     * @return 操作结果
     */
    VoidResult SendCommonMsgToObserver(ObjectId observer_id, const CommonMessage& msg, ObjectId source_id);

    /**
     * @brief 向观察者发送事件消息
     * @param msg 要发送的事件消息
     * @param source_id 消息源对象ID
     * @return 发送成功的观察者数量
     */
    Result<size_t> SendEventMsgToObserver(const EventMessage& msg, ObjectId source_id);

    /**
     * @brief 通过目标者关注的设备获取目标者的唯一标识
     * @param device_id 设备的标号
     * @param device_type 设备类型
     * @return 目标者唯一标识，失败返回错误
     */
    Result<ObjectId> GetSubjectIdByDevID(DeviceId device_id, DeviceCategory device_type) const;

    /**
     * @brief 获取统计信息
     * @return 包含注册对象数量等统计信息
     */
    struct Statistics {
        size_t observer_count{0};
        size_t subject_count{0};
        size_t device_mapping_count{0};
    };
    Statistics GetStatistics() const noexcept;

    /**
     * @brief 清理所有注册的对象（用于测试或重置）
     * @return 操作结果
     */
    VoidResult ClearAll() noexcept;

private:
    /**
     * @brief 根据观察者ID查找观察者对象信息
     * @param observer_id 观察者ID
     * @return 对象智能指针
     */
    std::shared_ptr<Observer> GetObserverInfoByID(ObjectId observer_id) const;

    /**
     * @brief 根据目标者ID查找目标者对象信息
     * @param subject_id 目标者ID
     * @return 对象智能指针
     */
    std::shared_ptr<Subject> GetSubjectInfoByID(ObjectId subject_id) const;

    /**
     * @brief 根据设备ID查找目标者对象信息
     * @param device_id 设备ID
     * @return 对象智能指针
     */
    std::shared_ptr<Subject> GetSubjectInfoByDevID(const DeviceUUID& device_id) const;

    /**
     * @brief 添加目标者管理的设备映射关系
     * @param subject 目标者对象智能指针
     * @return 操作结果
     */
    VoidResult AddDevToSubjectMap(std::shared_ptr<Subject> subject);

    /**
     * @brief 移除目标者管理的设备映射关系
     * @param subject 目标者对象智能指针
     * @return 操作结果
     */
    VoidResult RemoveDevToSubjectMap(std::shared_ptr<Subject> subject) noexcept;

private:
    /** @brief 单实例指针 */
    static std::shared_ptr<Mediator> instance_;

    /** @brief 单例创建互斥锁 */
    static std::once_flag instance_flag_;

    /** @brief 观察者映射表 */
    std::unordered_map<ObjectId, std::shared_ptr<Observer>> observer_map_;

    /** @brief 目标者映射表 */
    std::unordered_map<ObjectId, std::shared_ptr<Subject>> subject_map_;

    /** @brief 设备到目标者映射表 */
    std::map<DeviceUUID, std::shared_ptr<Subject>> dev_to_subject_map_;

    /** @brief 观察者映射表互斥锁 */
    mutable std::shared_mutex observer_mutex_;

    /** @brief 目标者映射表互斥锁 */
    mutable std::shared_mutex subject_mutex_;

    /** @brief 设备到目标者映射表互斥锁 */
    mutable std::shared_mutex dev_to_subject_mutex_;

};

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_MEDIATOR_H
