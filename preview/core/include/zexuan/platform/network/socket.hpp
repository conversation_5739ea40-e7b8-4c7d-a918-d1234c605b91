#ifndef ZEXUAN_PLATFORM_NETWORK_SOCKET_HPP
#define ZEXUAN_PLATFORM_NETWORK_SOCKET_HPP

#include "zexuan/platform/network/address.hpp"
#include "zexuan/platform/network/sockets_ops.hpp"

// struct tcp_info is in <netinet/tcp.h>
struct tcp_info;

namespace zexuan {
namespace platform {
namespace network {

class Socket {
public:
    explicit Socket(int sockfd) : sockfd_(sockfd) {}
    ~Socket();

    // 禁用拷贝构造和赋值
    Socket(const Socket&) = delete;
    Socket& operator=(const Socket&) = delete;

    int fd() const { return sockfd_; }

    bool getTcpInfo(struct tcp_info* tcpi) const;
    bool getTcpInfoString(char* buf, int len) const;

    /// abort if address in use
    void bindAddress(const Address& localaddr);
    /// abort if address in use
    void listen();

    /// On success, returns a non-negative integer that is
    /// a descriptor for the accepted socket, which has been
    /// set to non-blocking and close-on-exec. *peeraddr is assigned.
    /// On error, -1 is returned, and *peeraddr is untouched.
    int accept(Address* peeraddr);

    void shutdownWrite();

    void setTcpNoDelay(bool on);
    void setReuseAddr(bool on);
    void setReusePort(bool on);
    void setKeepAlive(bool on);

private:
    const int sockfd_;
};

} // namespace network
} // namespace platform
} // namespace zexuan

#endif // ZEXUAN_PLATFORM_NETWORK_SOCKET_HPP
