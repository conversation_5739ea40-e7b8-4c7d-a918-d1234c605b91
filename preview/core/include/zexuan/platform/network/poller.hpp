#ifndef ZEXUAN_PLATFORM_NETWORK_POLLER_HPP
#define ZEXUAN_PLATFORM_NETWORK_POLLER_HPP

#include <map>
#include <vector>
#include <memory>
#include <chrono>

// 前向声明
namespace zexuan::net {
    class Channel;
}

namespace zexuan::platform::network {

// 使用 net 层的 Channel
using Channel = ::zexuan::net::Channel;

class Poller {
public:
    using ChannelList = std::vector<Channel*>;
    using Timestamp = std::chrono::system_clock::time_point;

    Poller() = default;
    virtual ~Poller() = default;

    // 禁用拷贝
    Poller(const Poller&) = delete;
    Poller& operator=(const Poller&) = delete;

    // 核心接口
    virtual Timestamp poll(int timeoutMs, ChannelList* activeChannels) = 0;
    virtual void updateChannel(Channel* channel) = 0;
    virtual void removeChannel(Channel* channel) = 0;
    virtual bool hasChannel(Channel* channel) const = 0;

    static std::unique_ptr<Poller> createDefaultPoller();


protected:
    using ChannelMap = std::map<int, Channel*>;
    ChannelMap channels_;
};

} // namespace zexuan::platform::network

#endif // ZEXUAN_PLATFORM_NETWORK_POLLER_HPP
