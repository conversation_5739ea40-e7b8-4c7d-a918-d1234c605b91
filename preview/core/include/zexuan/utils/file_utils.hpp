#pragma once

#include <string>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <iostream>
namespace zexuan {
namespace utils {

/**
 * @brief 文件工具类
 * 
 * 提供文件相关的实用工具函数，包括文件名生成、路径处理等功能。
 */
class FileUtils {
public:
    /**
     * @brief 生成带时间戳和UUID的唯一文件名
     * @param extension 文件扩展名（包含点号，如 ".txt"）
     * @return 格式为 "YYYYMMDDHHMMSS_UUID.extension" 的唯一文件名
     *
     * 示例：
     * - generateUniqueFileName(".jpg") -> "20250730235959_550e8400-e29b-41d4-a716-************.jpg"
     */
    static std::string generateUniqueFileName(const std::string& extension);

    /**
     * @brief 基于文件创建时间生成新文件名
     * @param filePath 原文件路径
     * @return 格式为 "YYYYMMDDHHMMSS_UUID.原扩展名" 的新文件名
     *
     * 示例：
     * - generateFileNameFromCreationTime("/path/to/image.jpg") -> "20240823133804_c87398b4-0e7e-4ef5-9d8f-7e2dbde914cc.jpg"
     */
    static std::string generateFileNameFromCreationTime(const std::string& filePath);
    
    /**
     * @brief 生成系统级唯一的UUID字符串
     * @return UUID v4 格式的字符串
     *
     * 使用跨平台的 UUID 生成器生成真正的UUID，保证全局唯一性。
     */
    static std::string generateUUID();
    
    /**
     * @brief 生成时间戳字符串
     * @param format 时间格式字符串，默认为 "%Y%m%d%H%M%S"
     * @return 格式化的时间戳字符串
     */
    static std::string generateTimestamp(const std::string& format = "%Y%m%d%H%M%S");
    
    /**
     * @brief 检查文件扩展名是否有效
     * @param extension 扩展名字符串
     * @return 如果扩展名有效返回 true
     */
    static bool isValidExtension(const std::string& extension);
    
    /**
     * @brief 确保扩展名以点号开头
     * @param extension 扩展名字符串
     * @return 标准化的扩展名（以点号开头）
     */
    static std::string normalizeExtension(const std::string& extension);
};

} // namespace utils
} // namespace zexuan
