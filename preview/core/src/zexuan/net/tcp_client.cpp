#include "zexuan/net/tcp_client.hpp"
#include "zexuan/net/connector.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/platform/network/sockets_ops.hpp"
#include <spdlog/spdlog.h>

using namespace zexuan;
using namespace zexuan::net;

TcpClient::TcpClient(EventLoop* loop,
                     const zexuan::platform::network::Address& serverAddr,
                     const std::string& nameArg)
    : loop_(loop),
      connector_(new Connector(loop, serverAddr)),
      name_(nameArg),
      retry_(false),
      connect_(true),
      nextConnId_(1) {
    connector_->setNewConnectionCallback(
        [this](int sockfd) { newConnection(sockfd); });
    // FIXME setConnectFailedCallback
    spdlog::debug("TcpClient::TcpClient[{}] - connector {}", name_, static_cast<void*>(connector_.get()));
}

TcpClient::~TcpClient() {
    spdlog::debug("TcpClient::~TcpClient[{}] - connector {}", name_, static_cast<void*>(connector_.get()));

    TcpConnectionPtr conn;
    bool unique = false;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        unique = connection_.unique();
        conn = connection_;
    }

    if (conn) {
        assert(loop_ == conn->getLoop());
        // FIXME: not 100% safe, if we are in different thread
        CloseCallback cb = [this](const TcpConnectionPtr& conn) {
            // FIXME: unsafe
            loop_->runInLoop([this, conn]() { removeConnection(conn); });
        };
        loop_->runInLoop([conn, cb]() { conn->setCloseCallback(cb); });
        if (unique) {
            conn->forceClose();
        }
    } else {
        connector_->stop();
        // FIXME: HACK - implement timer functionality
        // loop_->runAfter(1, [this]() { connector_->stop(); });
    }
}

void TcpClient::connect() {
    // FIXME: check state
    spdlog::debug("TcpClient::connect[{}] - connecting to {}", name_, connector_->serverAddress().toIpPort());

    connect_ = true;
    connector_->start();
}

void TcpClient::disconnect() {
    connect_ = false;

    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (connection_) {
            connection_->shutdown();
        }
    }
}

void TcpClient::stop() {
    connect_ = false;
    connector_->stop();
}

void TcpClient::newConnection(int sockfd) {
    loop_->assertInLoopThread();
    zexuan::platform::network::Address peerAddr(zexuan::platform::network::sockets::getPeerAddr(sockfd));
    char buf[32];
    snprintf(buf, sizeof buf, ":%s#%d", peerAddr.toIpPort().c_str(), nextConnId_);
    ++nextConnId_;
    std::string connName = name_ + buf;

    zexuan::platform::network::Address localAddr(zexuan::platform::network::sockets::getLocalAddr(sockfd));
    // FIXME poll with zero timeout to double confirm the new connection
    // FIXME use make_shared if necessary
    TcpConnectionPtr conn(new TcpConnection(loop_,
                                            connName,
                                            sockfd,
                                            localAddr,
                                            peerAddr));

    conn->setConnectionCallback(connectionCallback_);
    conn->setMessageCallback(messageCallback_);
    conn->setWriteCompleteCallback(writeCompleteCallback_);
    // always enable reading when writing complete
    conn->setCloseCallback(
        [this](const TcpConnectionPtr& conn) {
            removeConnection(conn);
        });
    {
        std::lock_guard<std::mutex> lock(mutex_);
        connection_ = conn;
    }

    conn->connectEstablished();
}

void TcpClient::removeConnection(const TcpConnectionPtr& conn) {
    loop_->assertInLoopThread();
    assert(loop_ == conn->getLoop());

    {
        std::lock_guard<std::mutex> lock(mutex_);
        assert(connection_ == conn);
        connection_.reset();
    }

    loop_->queueInLoop([conn]() { conn->connectDestroyed(); });
    if (retry_ && connect_) {
        spdlog::debug("TcpClient::connect[{}] - Reconnecting to {}", 
                     conn->name(), connector_->serverAddress().toIpPort());

        connector_->restart();
    }
}

