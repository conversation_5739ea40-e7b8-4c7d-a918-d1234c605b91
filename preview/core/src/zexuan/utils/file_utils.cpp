#include "zexuan/utils/file_utils.hpp"
#include "zexuan/platform/utils/uuid.hpp"
#include <stdexcept>
#include <filesystem>
#include <sys/stat.h>

namespace zexuan {
namespace utils {

std::string FileUtils::generateUniqueFileName(const std::string& extension) {
    // 生成当前时间戳
    std::string timestamp = generateTimestamp();

    // 生成UUID
    std::string uuid = generateUUID();

    // 标准化扩展名
    std::string normalized_ext = normalizeExtension(extension);

    // 组合文件名：时间戳_UUID.扩展名
    return timestamp + "_" + uuid + normalized_ext;
}

std::string FileUtils::generateFileNameFromCreationTime(const std::string& filePath) {
    try {
        // 获取文件状态信息
        struct stat fileStat;
        if (stat(filePath.c_str(), &fileStat) != 0) {
            throw std::runtime_error("Failed to get file statistics for: " + filePath);
        }

        // 获取文件创建时间（在Linux上使用修改时间作为替代）
        std::time_t creationTime = fileStat.st_mtime;

        // 格式化文件创建时间为 YYYYMMDDHHMMSS 格式
        std::stringstream ss;
        ss << std::put_time(std::localtime(&creationTime), "%Y%m%d%H%M%S");
        std::string timestamp = ss.str();

        // 生成UUID
        std::string uuid = generateUUID();

        // 获取原文件扩展名
        std::filesystem::path path(filePath);
        std::string extension = path.extension().string();
        std::string normalized_ext = normalizeExtension(extension);

        // 组合文件名：文件创建时间_UUID.原扩展名
        return timestamp + "_" + uuid + normalized_ext;

    } catch (const std::exception& e) {
        // 如果获取文件时间失败，回退到当前时间
        std::cerr << "Failed to get file creation time, using current time: " << e.what() << std::endl;
        return generateUniqueFileName(std::filesystem::path(filePath).extension().string());
    }
}

std::string FileUtils::generateUUID() {
    // 使用跨平台的 UUID 生成器
    auto uuid = platform::utils::uuid::GenerateRandom();
    return platform::utils::uuid::ToString(uuid);
}

std::string FileUtils::generateTimestamp(const std::string& format) {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), format.c_str());
    
    return ss.str();
}

bool FileUtils::isValidExtension(const std::string& extension) {
    if (extension.empty()) {
        return false;
    }
    
    // 检查是否包含非法字符
    const std::string invalid_chars = "\\/:*?\"<>|";
    for (char c : extension) {
        if (invalid_chars.find(c) != std::string::npos) {
            return false;
        }
    }
    
    return true;
}

std::string FileUtils::normalizeExtension(const std::string& extension) {
    if (extension.empty()) {
        return "";
    }
    
    // 如果扩展名不以点号开头，添加点号
    if (extension[0] != '.') {
        return "." + extension;
    }
    
    return extension;
}

} // namespace utils
} // namespace zexuan
