/**
 * @file invoke_id_utils.cpp
 * @brief invoke_id 工具函数实现 - 使用跨平台 UUID
 * <AUTHOR> from NX framework
 * @date 2025-08-16
 */

#include "zexuan/utils/invoke_id_utils.hpp"
#include "zexuan/platform/utils/uuid.hpp"
#include <format>

namespace zexuan {
namespace utils {
namespace invoke_id {

namespace {
    /// @brief 分隔符常量
    constexpr const char* SEPARATOR = "#ZX#";
    constexpr size_t SEPARATOR_LEN = 4;
}

std::string Generate(int object_id, const std::string& sequence) {
    std::string seq = sequence;

    // 如果没有提供序列号，生成一个 UUID
    if (seq.empty()) {
        auto uuid = platform::utils::uuid::GenerateRandom();
        seq = platform::utils::uuid::ToCompactString(uuid);
    }

    // 格式：对象ID#ZX#序列号
    return std::format("{}{}{}", object_id, SEPARATOR, seq);
}

int ParseObjectId(const std::string& invoke_id) {
    // 查找分隔符 "#ZX#"
    auto pos = invoke_id.find(SEPARATOR);
    if (pos == std::string::npos) {
        return -1;
    }
    
    try {
        // 提取对象ID部分
        std::string id_str = invoke_id.substr(0, pos);
        return std::stoi(id_str);
    } catch (...) {
        return -1;
    }
}

std::string ParseSequence(const std::string& invoke_id) {
    // 查找分隔符 "#ZX#"
    auto pos = invoke_id.find(SEPARATOR);
    if (pos == std::string::npos) {
        return "";
    }
    
    // 提取序列号部分
    return invoke_id.substr(pos + SEPARATOR_LEN);
}

bool IsValid(const std::string& invoke_id) {
    // 检查是否包含分隔符
    auto pos = invoke_id.find(SEPARATOR);
    if (pos == std::string::npos) {
        return false;
    }
    
    // 检查对象ID部分是否为有效数字
    std::string id_part = invoke_id.substr(0, pos);
    if (id_part.empty()) {
        return false;
    }
    
    try {
        std::stoi(id_part);
    } catch (...) {
        return false;
    }
    
    // 检查序列号部分是否存在
    std::string seq_part = invoke_id.substr(pos + SEPARATOR_LEN);
    if (seq_part.empty()) {
        return false;
    }
    
    return true;
}

} // namespace invoke_id
} // namespace utils
} // namespace zexuan
