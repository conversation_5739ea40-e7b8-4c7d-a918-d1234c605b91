# 创建 protocol_server 动态库
add_library(protocol_server SHARED
    src/protocol_server.cpp
)

# 链接必要的库
target_link_libraries(protocol_server
    PRIVATE
        core
        protocol_interface
)

# 设置包含目录
target_include_directories(protocol_server PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 设置输出目录和版本
set_target_properties(protocol_server PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/libs/server"
    VERSION 1.0.0
    SOVERSION 1
)