#include "../include/tcpserver.hpp"
#include "zexuan/protocol/gateway/protocol_gateway.hpp"
#include <spdlog/spdlog.h>

namespace zexuan {
namespace server {

ProtocolServer::ProtocolServer() {
    spdlog::info("ProtocolServer created");
}

ProtocolServer::~ProtocolServer() {
    Shutdown();
    spdlog::info("ProtocolServer destroyed");
}

bool ProtocolServer::Initialize(const ProtocolServerConfig& config) {
    if (is_initialized_.load()) {
        spdlog::warn("ProtocolServer already initialized");
        return true;
    }

    config_ = config;

    try {
        // 创建协议工厂
        protocol_factory_ = std::make_shared<protocol::interface::ProtocolFactory>();



        // 创建EventLoop
        event_loop_ = std::make_unique<net::EventLoop>();

        // 创建TcpServer
        zexuan::platform::network::Address listen_addr(config_.listen_address, config_.listen_port);
        tcp_server_ = std::make_unique<net::TcpServer>(event_loop_.get(), listen_addr, "ProtocolServer");

        // 设置回调
        tcp_server_->setConnectionCallback([this](const net::TcpConnectionPtr& conn) {
            OnConnection(conn);
        });

        tcp_server_->setMessageCallback([this](const net::TcpConnectionPtr& conn, net::Buffer* buffer, net::Timestamp receiveTime) {
            OnMessage(conn, buffer, receiveTime);
        });

        tcp_server_->setWriteCompleteCallback([this](const net::TcpConnectionPtr& conn) {
            OnWriteComplete(conn);
        });

        // 设置线程池大小
        tcp_server_->setThreadNum(config_.thread_pool_size);

        is_initialized_.store(true);
        spdlog::info("ProtocolServer initialized successfully on {}:{}", config_.listen_address, config_.listen_port);
        return true;

    } catch (const std::exception& e) {
        spdlog::error("Failed to initialize ProtocolServer: {}", e.what());
        return false;
    }
}

bool ProtocolServer::Start() {
    if (!is_initialized_.load()) {
        spdlog::error("ProtocolServer not initialized");
        return false;
    }

    if (is_running_.load()) {
        spdlog::warn("ProtocolServer already running");
        return true;
    }

    try {
        // 启动TcpServer
        tcp_server_->start();

        is_running_.store(true);
        spdlog::info("ProtocolServer started successfully");

        // 运行事件循环（这会阻塞）
        event_loop_->loop();

        return true;

    } catch (const std::exception& e) {
        spdlog::error("Failed to start ProtocolServer: {}", e.what());
        return false;
    }
}

bool ProtocolServer::Stop() {
    if (!is_running_.load()) {
        spdlog::warn("ProtocolServer not running");
        return true;
    }

    try {
        // 停止事件循环
        event_loop_->quit();

        is_running_.store(false);
        spdlog::info("ProtocolServer stopped successfully");
        return true;

    } catch (const std::exception& e) {
        spdlog::error("Failed to stop ProtocolServer: {}", e.what());
        return false;
    }
}

void ProtocolServer::Shutdown() {
    Stop();

    // 清理所有连接和Gateway
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        for (const auto& [conn_id, conn] : connections_) {
            if (conn && conn->connected()) {
                conn->forceClose();
            }
            DestroyGatewayForConnection(conn_id);
        }
        connections_.clear();
        conn_name_to_id_.clear();
    }



    // 清理网络组件
    tcp_server_.reset();
    event_loop_.reset();

    is_initialized_.store(false);
    spdlog::info("ProtocolServer shutdown completed");
}

bool ProtocolServer::SendData(uint32_t conn_id, const std::vector<uint8_t>& data) {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    auto it = connections_.find(conn_id);
    if (it != connections_.end() && it->second->connected()) {
        it->second->send(data.data(), data.size());
        stats_.bytes_sent += data.size();
        spdlog::debug("Sent {} bytes to connection {}", data.size(), conn_id);
        return true;
    }

    spdlog::error("Failed to send data: connection {} not found or disconnected", conn_id);
    return false;
}

// 网络回调处理
void ProtocolServer::OnConnection(const net::TcpConnectionPtr& conn) {
    if (conn->connected()) {
        // 新连接建立
        uint32_t conn_id = AddConnection(conn);

        // 为连接创建Gateway
        if (CreateGatewayForConnection(conn_id)) {
            stats_.total_connections++;
            stats_.active_connections++;
            spdlog::info("New connection established: {} (ID: {})", conn->name(), conn_id);
        } else {
            spdlog::error("Failed to create Gateway for connection: {} (ID: {})", conn->name(), conn_id);
            conn->forceClose();
        }
    } else {
        // 连接断开
        RemoveConnection(conn);
        spdlog::info("Connection closed: {}", conn->name());
    }
}

void ProtocolServer::OnMessage(const net::TcpConnectionPtr& conn, net::Buffer* buffer, net::Timestamp receiveTime) {
    // 获取连接ID
    uint32_t conn_id;
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        auto it = conn_name_to_id_.find(conn->name());
        if (it == conn_name_to_id_.end()) {
            spdlog::error("Unknown connection: {}", conn->name());
            return;
        }
        conn_id = it->second;
    }

    // 读取所有数据
    std::vector<uint8_t> data(buffer->readableBytes());
    std::copy(buffer->peek(), buffer->peek() + buffer->readableBytes(), data.begin());
    buffer->retrieveAll();

    stats_.bytes_received += data.size();

    // 直接调用对应的Gateway处理协议数据
    {
        std::lock_guard<std::mutex> lock(gateways_mutex_);
        auto gateway_it = gateways_.find(conn_id);
        if (gateway_it != gateways_.end()) {
            // 直接调用Gateway的协议数据处理回调
            gateway_it->second->OnNetworkProtocolData(data, conn_id);
            spdlog::debug("Sent {} bytes to Gateway for connection {}", data.size(), conn_id);
        } else {
            spdlog::error("No Gateway found for connection {}", conn_id);
        }
    }
}

void ProtocolServer::OnWriteComplete(const net::TcpConnectionPtr& conn) {
    spdlog::debug("Write completed for connection: {}", conn->name());
}

// 连接管理方法
uint32_t ProtocolServer::GenerateConnectionId() {
    return next_connection_id_.fetch_add(1);
}

uint32_t ProtocolServer::AddConnection(const net::TcpConnectionPtr& conn) {
    std::lock_guard<std::mutex> lock(connections_mutex_);

    uint32_t conn_id = GenerateConnectionId();
    connections_[conn_id] = conn;
    conn_name_to_id_[conn->name()] = conn_id;

    return conn_id;
}

void ProtocolServer::RemoveConnection(const net::TcpConnectionPtr& conn) {
    std::lock_guard<std::mutex> lock(connections_mutex_);

    auto name_it = conn_name_to_id_.find(conn->name());
    if (name_it != conn_name_to_id_.end()) {
        uint32_t conn_id = name_it->second;

        // 销毁对应的Gateway
        DestroyGatewayForConnection(conn_id);

        // 移除连接
        connections_.erase(conn_id);
        conn_name_to_id_.erase(name_it);

        stats_.active_connections--;
        spdlog::info("Connection removed: {} (ID: {})", conn->name(), conn_id);
    }
}

// Gateway管理方法
bool ProtocolServer::CreateGatewayForConnection(uint32_t connection_id) {
    try {
        // 获取对应的连接
        net::TcpConnectionPtr connection;
        {
            std::lock_guard<std::mutex> lock(connections_mutex_);
            auto it = connections_.find(connection_id);
            if (it == connections_.end()) {
                spdlog::error("Connection {} not found", connection_id);
                return false;
            }
            connection = it->second;
        }

        // 创建 Mediator（每个Gateway需要独立的Mediator）
        auto mediator = std::make_shared<base::Mediator>();

        // 创建 Gateway，注入连接
        auto gateway = std::make_shared<zexuan::protocol::gateway::ProtocolGateway>(
            mediator, connection_id);

        // 设置Gateway的发送回调，让它能够通过TCPServer发送数据
        gateway->SetSendCallback([this](uint32_t conn_id, const std::vector<uint8_t>& data) -> bool {
            return SendData(conn_id, data);
        });

        // 启动Gateway
        if (!gateway->Start()) {
            spdlog::error("Failed to start Gateway for connection {}", connection_id);
            return false;
        }

        // 存储Gateway
        {
            std::lock_guard<std::mutex> lock(gateways_mutex_);
            gateways_[connection_id] = gateway;
        }

        spdlog::info("Created Gateway for connection {}", connection_id);
        return true;

    } catch (const std::exception& e) {
        spdlog::error("Exception creating Gateway for connection {}: {}", connection_id, e.what());
        return false;
    }
}

void ProtocolServer::DestroyGatewayForConnection(uint32_t connection_id) {
    std::lock_guard<std::mutex> lock(gateways_mutex_);
    auto it = gateways_.find(connection_id);
    if (it != gateways_.end()) {
        // 停止Gateway
        it->second->Stop();

        // 移除Gateway
        gateways_.erase(it);

        spdlog::info("Destroyed Gateway for connection {}", connection_id);
    }
}




// 状态查询方法
size_t ProtocolServer::GetConnectionCount() const {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    return connections_.size();
}

std::vector<uint32_t> ProtocolServer::GetAllConnectionIds() const {
    std::lock_guard<std::mutex> lock(connections_mutex_);

    std::vector<uint32_t> ids;
    ids.reserve(connections_.size());

    for (const auto& [conn_id, _] : connections_) {
        ids.push_back(conn_id);
    }

    return ids;
}

void ProtocolServer::ResetStatistics() {
    stats_.total_connections.store(0);
    stats_.active_connections.store(0);
    stats_.bytes_sent.store(0);
    stats_.bytes_received.store(0);
}

} // namespace server
} // namespace zexuan