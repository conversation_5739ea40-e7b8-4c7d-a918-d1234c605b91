#pragma once

#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <atomic>
#include <mutex>
#include <functional>
#include <spdlog/spdlog.h>

// 引入现有的网络库
#include "zexuan/net/tcp_server.hpp"
#include "zexuan/net/tcp_connection.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/callbacks.hpp"
#include "zexuan/net/buffer.hpp"
#include "zexuan/platform/network/address.hpp"

// 引入协议工厂
#include "protocol_interface.hpp"

namespace zexuan {
namespace server {

/**
 * @brief 协议服务器配置
 */
struct ProtocolServerConfig {
    std::string listen_address = "0.0.0.0";
    uint16_t listen_port = 8080;
    int max_connections = 100;
    int thread_pool_size = 4;
};

/**
 * @brief 协议服务器类 - 基于现有网络库的简单封装
 *
 * 职责：
 * 1. 管理TCP连接和对应的ProtocolGateway
 * 2. 直接回调Gateway处理协议数据
 * 3. 利用现有的网络库处理TCP连接
 */
class ProtocolServer {
public:

    explicit ProtocolServer();
    virtual ~ProtocolServer();

    // 生命周期管理
    bool Initialize(const ProtocolServerConfig& config);
    bool Start();
    bool Stop();
    void Shutdown();

    // 状态查询
    bool IsRunning() const { return is_running_.load(); }
    size_t GetConnectionCount() const;
    std::vector<uint32_t> GetAllConnectionIds() const;

    // 统计信息
    struct Statistics {
        std::atomic<uint64_t> total_connections{0};
        std::atomic<uint64_t> active_connections{0};
        std::atomic<uint64_t> bytes_sent{0};
        std::atomic<uint64_t> bytes_received{0};
    };

    const Statistics& GetStatistics() const { return stats_; }
    void ResetStatistics();

    /**
     * @brief 发送数据到指定连接
     * @param conn_id 连接ID
     * @param data 数据
     * @return 是否发送成功
     */
    bool SendData(uint32_t conn_id, const std::vector<uint8_t>& data);

private:
    // 网络回调处理
    void OnConnection(const net::TcpConnectionPtr& conn);
    void OnMessage(const net::TcpConnectionPtr& conn, net::Buffer* buffer, net::Timestamp receiveTime);
    void OnWriteComplete(const net::TcpConnectionPtr& conn);

    // 连接管理
    uint32_t GenerateConnectionId();
    uint32_t AddConnection(const net::TcpConnectionPtr& conn);
    void RemoveConnection(const net::TcpConnectionPtr& conn);

    // Gateway管理
    bool CreateGatewayForConnection(uint32_t connection_id);
    void DestroyGatewayForConnection(uint32_t connection_id);



private:

    // 网络组件
    std::unique_ptr<net::EventLoop> event_loop_;
    std::unique_ptr<net::TcpServer> tcp_server_;

    // 协议组件
    std::shared_ptr<protocol::interface::IProtocolFactory> protocol_factory_;

    // 连接管理
    std::unordered_map<std::string, uint32_t> conn_name_to_id_;  // TcpConnection名称到ID的映射
    std::unordered_map<uint32_t, net::TcpConnectionPtr> connections_;  // ID到连接的映射
    std::atomic<uint32_t> next_connection_id_{1};
    mutable std::mutex connections_mutex_;

    // Gateway管理
    std::unordered_map<uint32_t, std::shared_ptr<zexuan::protocol::gateway::ProtocolGateway>> gateways_;
    mutable std::mutex gateways_mutex_;

    // 配置和状态
    ProtocolServerConfig config_;
    std::atomic<bool> is_initialized_{false};
    std::atomic<bool> is_running_{false};

    // 统计信息
    Statistics stats_;
};

} // namespace server
} // namespace zexuan

