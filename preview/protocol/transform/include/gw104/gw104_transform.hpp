#ifndef GW104_TRANSFORM_HPP
#define GW104_TRANSFORM_HPP

#include "../protocol_transform.hpp"
#include "zexuan/base/base_types.hpp"
#include <map>

namespace zexuan {
namespace protocol {
namespace transform {
namespace gw104 {

// IEC104 相关常量
constexpr uint8_t IEC104_START_BYTE = 0x68;
constexpr uint8_t IEC104_APCI_LENGTH = 6;

// IEC104 ASDU 类型
enum class AsduType : uint8_t {
    M_SP_NA_1 = 1    // 单点信息
};

// IEC104 传输原因
enum class CauseOfTransmission : uint8_t {
    PERIODIC = 1         // 周期、循环
};

/**
 * @brief IEC104 协议转换器
 * 参考原始的 GW104 实现
 */
class GW104Transform : public ProtocolTransform {
public:
    GW104Transform();
    virtual ~GW104Transform() = default;

    // 实现基类接口
    int ConvertProToCommonMsg(
        const base::ProtocolFrameList& frame_list,
        std::vector<base::CommonMessage>& common_list,
        base::ProtocolFrameList& result_frames) override;

    // 其他方法暂时空实现
    int ConvertProToEventMsg(
        const base::ProtocolFrame& frame,
        std::vector<base::EventMessage>& event_list) override;

    int ConvertEventMsgToPro(
        const base::EventMessage& event_msg,
        base::ProtocolFrameList& frame_list) override;

    int ConvertCommonMsgToPro(
        const base::CommonMessage& common_msg,
        base::ProtocolFrameList& cmd_frames,
        base::ProtocolFrameList& result_frames) override;

    // 新增的多帧处理方法
    base::ProtocolConvertType GetConvertTypeByFrame(const base::ProtocolFrame& frame) override;
    bool IsFollowUpFrame(const base::ProtocolFrame& src_frame, const base::ProtocolFrame& dst_frame) override;
    bool UpdateFrameBySrcFrame(const base::ProtocolFrame& src_frame, base::ProtocolFrame& dst_frame) override;

private:
    // IEC104 ASDU 结构
    struct AsduHeader {
        AsduType type_id;
        uint8_t vsq;                    // 可变结构限定词
        CauseOfTransmission cot;        // 传输原因
        uint8_t org_addr;               // 源地址
        uint16_t asdu_addr;             // ASDU地址
    };

    // 核心解析方法
    bool ParseAsduHeader(const std::vector<uint8_t>& data, size_t offset, AsduHeader& header);
    bool ValidateFrame(const std::vector<uint8_t>& data);

    // 协议帧类型判断方法（参考原始 _GetAsduXXCvtType 系列方法）
    base::ProtocolConvertType GetAsdu1ConvertType(const base::ProtocolFrame& frame);
    base::ProtocolConvertType GetAsdu2ConvertType(const base::ProtocolFrame& frame);
    base::ProtocolConvertType GetDefaultConvertType(const base::ProtocolFrame& frame);

    // 测试接口
public:
    /**
     * @brief 测试接口 - 创建一个简单的总召唤响应
     * @param asdu_addr ASDU地址
     * @param frame_list 输出帧列表
     * @return 转换结果
     */
    int CreateTestInterrogationResponse(uint16_t asdu_addr, base::ProtocolFrameList& frame_list);

    /**
     * @brief 测试接口 - 解析简单的总召唤命令
     * @param frame_list 输入帧列表
     * @param common_list 输出消息列表
     * @return 转换结果
     */
    int ParseTestInterrogationCommand(const base::ProtocolFrameList& frame_list,
                                     std::vector<base::CommonMessage>& common_list);
};

} // namespace gw104
} // namespace transform
} // namespace protocol
} // namespace zexuan

#endif // GW104_TRANSFORM_HPP
