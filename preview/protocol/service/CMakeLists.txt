# Protocol Service Layer CMakeLists.txt

# 创建 protocol_service 动态库
add_library(protocol_service SHARED
    src/protocol_service.cpp
)

# 链接必要的库
target_link_libraries(protocol_service
    PRIVATE
        core
        protocol_transform
)

# 设置包含目录
target_include_directories(protocol_service
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
        ${CMAKE_SOURCE_DIR}/protocol/transform/include
)

# 设置输出目录和版本
set_target_properties(protocol_service PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/libs/protocol"
    VERSION 1.0.0
    SOVERSION 1
)