# Protocol Gateway Layer CMakeLists.txt

# 创建 protocol_gateway 动态库
add_library(protocol_gateway SHARED
    src/protocol_gateway.cpp
)

# 链接必要的库
target_link_libraries(protocol_gateway
    PRIVATE
        core
        protocol_transform
        protocol_service
)

# 设置包含目录
target_include_directories(protocol_gateway
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
        ${CMAKE_SOURCE_DIR}/protocol/transform/include
        ${CMAKE_SOURCE_DIR}/protocol/service/include
)

# 设置输出目录和版本
set_target_properties(protocol_gateway PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/libs/protocol"
    VERSION 1.0.0
    SOVERSION 1
)