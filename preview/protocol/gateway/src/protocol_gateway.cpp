#include "protocol_gateway.hpp"
#include "protocol_transform.hpp"
#include "protocol_service.hpp"
#include "zexuan/utils/invoke_id_utils.hpp"
#include <spdlog/spdlog.h>
#include <fstream>

namespace zexuan {
namespace protocol {
namespace gateway {

ProtocolGateway::ProtocolGateway(const std::string& config_file_path, std::shared_ptr<base::Mediator> mediator)
    : mediator_(mediator), config_file_path_(config_file_path) {

    // 读取配置文件
    std::ifstream config_file(config_file_path_);
    if (!config_file.is_open()) {
        spdlog::warn("Cannot open config file: {}, using default values", config_file_path_);
        // 使用默认值
        request_timeout_seconds_ = 30;
        thread_sleep_ms_ = 10;
        max_pending_requests_ = 1000;
        enable_multi_frame_ = true;
    } else {
        nlohmann::json config;
        config_file >> config;

        // 读取网关配置
        auto gateway_config = config["protocol"]["gateway"];
        request_timeout_seconds_ = gateway_config.value("request_timeout_seconds", 30);
        thread_sleep_ms_ = gateway_config.value("thread_sleep_ms", 10);
        max_pending_requests_ = gateway_config.value("max_pending_requests", 1000);
        enable_multi_frame_ = gateway_config.value("enable_multi_frame", true);
        protocol_type_ = gateway_config.value("protocol_type", "gw104");
    }
    
    // 创建Observer - 使用固定ID接收来自Service层的响应和事件
    observer_ = std::make_shared<base::Observer>(base::GATEWAY_OBSERVER_ID, mediator_);

    // 设置Observer的结果回调 - 处理来自Service层的响应
    observer_->SetResultCallback([this](const base::CommonMessage& message) -> base::Result<void> {
        AddServiceResponseToMultiFrame(message);
        return base::Result<void>{};
    });

    // 设置Observer的事件回调 - 处理来自Service层的事件
    observer_->SetEventCallback([this](const base::EventMessage& message) -> base::Result<void> {
        OnServiceEvent(message);
        return base::Result<void>{};
    });

    // 创建Subject - 使用固定ID向Service层发送命令
    subject_ = std::make_shared<base::Subject>(base::GATEWAY_SUBJECT_ID, mediator_);

    // 创建协议转换器
    if (!CreateProtocolTransform()) {
        spdlog::error("Failed to create protocol transform");
        throw std::runtime_error("Failed to create protocol transform");
    }

    // 创建协议服务
    if (!CreateProtocolService()) {
        spdlog::error("Failed to create protocol service");
        throw std::runtime_error("Failed to create protocol service");
    }

    spdlog::info("ProtocolGateway created with Observer ID: {}, Subject ID: {}, protocol: {}",
                 base::GATEWAY_OBSERVER_ID, base::GATEWAY_SUBJECT_ID, protocol_type_);
}



ProtocolGateway::~ProtocolGateway() {
    Stop();
    spdlog::info("ProtocolGateway destroyed");
}

bool ProtocolGateway::Start() {
    if (is_running_.load()) {
        spdlog::warn("Gateway already running");
        return true;
    }

    try {
        // 初始化Observer（包含注册到Mediator）
        auto observer_result = observer_->Init();
        if (!observer_result) {
            spdlog::error("Failed to initialize Observer: {}", static_cast<int>(observer_result.error()));
            return false;
        }

        // 初始化Subject（包含注册到Mediator）
        auto subject_result = subject_->Init();
        if (!subject_result) {
            spdlog::error("Failed to initialize Subject: {}", static_cast<int>(subject_result.error()));
            return false;
        }

        // 启动4个核心线程
        should_stop_.store(false);
        protocol_frame_processor_thread_ = std::thread(&ProtocolGateway::ProtocolFrameProcessorLoop, this);
        local_task_processor_thread_ = std::thread(&ProtocolGateway::LocalTaskProcessorLoop, this);
        multi_frame_matcher_thread_ = std::thread(&ProtocolGateway::MultiFrameMatcherLoop, this);
        event_processor_thread_ = std::thread(&ProtocolGateway::EventProcessorLoop, this);

        is_running_.store(true);
        spdlog::info("Gateway started successfully");
        return true;

    } catch (const std::exception& e) {
        spdlog::error("Failed to start Gateway: {}", e.what());
        return false;
    }
}

void ProtocolGateway::Stop() {
    if (!is_running_.load()) {
        return;
    }

    spdlog::info("Stopping Gateway");
    
    // 停止线程
    should_stop_.store(true);
    
    // 通知所有条件变量
    protocol_cmd_queue_cv_.notify_all();
    local_task_queue_cv_.notify_all();
    event_queue_cv_.notify_all();

    // 等待4个核心线程结束
    if (protocol_frame_processor_thread_.joinable()) protocol_frame_processor_thread_.join();
    if (local_task_processor_thread_.joinable()) local_task_processor_thread_.join();
    if (multi_frame_matcher_thread_.joinable()) multi_frame_matcher_thread_.join();
    if (event_processor_thread_.joinable()) event_processor_thread_.join();

    // 使用Exit()方法注销Observer和Subject
    if (observer_) observer_->Exit();
    if (subject_) subject_->Exit();

    is_running_.store(false);
    spdlog::info("Gateway stopped");
}

void ProtocolGateway::OnNetworkProtocolData(const std::vector<uint8_t>& protocol_data, uint32_t conn_id) {
    spdlog::debug("Gateway received protocol data from connection {}, length: {}",
                 conn_id, protocol_data.size());

    // 解析协议帧
    base::ProtocolFrame frame;
    if (!ParseProtocolFrame(protocol_data, frame)) {
        spdlog::error("Failed to parse protocol frame from connection {}", conn_id);
        return;
    }

    // 设置连接ID
    frame.frame_id = conn_id;

    // 简单地将协议帧放入原始队列，让 ProtocolFrameProcessorLoop 处理（参考原始 m_ProCmdDeque）
    {
        std::lock_guard<std::mutex> lock(protocol_cmd_queue_mutex_);
        protocol_cmd_queue_.push(frame);
    }
    protocol_cmd_queue_cv_.notify_one();
}

// 新增的协议帧处理方法实现

void ProtocolGateway::HandleSingleFrameCommand(const base::ProtocolFrame& frame) {
    // 单帧召唤命令处理（参考原始 CVT_TO_CALL 处理）
    spdlog::debug("Handling single frame command from connection {}", frame.frame_id);

    base::ProtocolFrameList frame_list = {frame};
    std::vector<base::CommonMessage> common_list;
    base::ProtocolFrameList result_frames;

    int ret = protocol_transform_->ConvertProToCommonMsg(frame_list, common_list, result_frames);
    if (ret != 0) {
        spdlog::error("Failed to convert single frame to common message");
        return;
    }

    // 处理转换失败生成的回应结果
    if (!result_frames.empty()) {
        for (const auto& result_frame : result_frames) {
            if (send_callback_) {
                send_callback_(frame.frame_id, result_frame.data);
            }
        }
        return; // 转换失败，直接返回，不需要等待响应
    }

    // 处理转换成功的通用消息 - 需要创建匹配包等待响应
    if (!common_list.empty()) {
        std::string request_key = GenerateMultiFrameRequestKey(frame, frame.frame_id);

        // 发送所有转换成功的通用消息
        for (auto& common_msg : common_list) {
            common_msg.source_id = frame.frame_id;
            common_msg.target_id = base::SERVICE_SUBJECT_ID;

            // 自己生成invoke_id，确保匹配安全
            std::string invoke_id = utils::invoke_id::Generate(base::GATEWAY_OBSERVER_ID);
            common_msg.invoke_id = invoke_id;

            if (observer_) {
                auto result = observer_->SendCommand(common_msg, base::SERVICE_SUBJECT_ID);
                if (result) {
                    spdlog::debug("Sent single frame command to Service: invoke_id={}", common_msg.invoke_id);

                    // 创建匹配包（使用我们自己生成的invoke_id）
                    std::lock_guard<std::mutex> lock(multi_frame_requests_mutex_);

                    MultiFrameRequest single_request;
                    single_request.frame_list.push_back(frame);
                    single_request.is_ctrl_cmd = false;  // 单帧召唤不是控制命令
                    single_request.received_last_cmd = true;  // 单帧，已经是最后一帧
                    single_request.commands_sent = true;  // 已经发送给Service
                    single_request.conn_id = frame.frame_id;
                    single_request.sent_invoke_id = invoke_id;  // 记录我们生成的invoke_id

                    multi_frame_requests_[request_key] = single_request;
                    spdlog::debug("Created single-frame request record: key={}, invoke_id={}", request_key, single_request.sent_invoke_id);

                } else {
                    spdlog::error("Failed to send single frame command to Service");
                    return;
                }
            }
        }
    }
}

void ProtocolGateway::HandleMultiFrameCommand(const base::ProtocolFrame& frame) {
    // 多帧控制命令处理（参考原始 CVT_TO_CTRL 处理）
    spdlog::debug("Handling multi-frame command from connection {}", frame.frame_id);

    // 添加到多帧处理队列
    if (!AddFrameToMultiFrameRequest(frame, frame.frame_id)) {
        spdlog::error("Failed to add frame to multi-frame request");
        return;
    }
}

void ProtocolGateway::HandleLocalResponse(const base::ProtocolFrame& frame) {
    // 本地直接响应处理（参考原始 CVT_FROM_LOCAL 处理）
    spdlog::debug("Handling local response frame from connection {}", frame.frame_id);

    // 放入本地任务队列，由 LocalTaskThreadLoop 处理
    {
        std::lock_guard<std::mutex> lock(local_task_queue_mutex_);
        local_task_queue_.push(frame);
    }
    local_task_queue_cv_.notify_one();
}

void ProtocolGateway::HandleEventFrame(const base::ProtocolFrame& frame) {
    // 事件帧处理（参考原始 CVT_TO_EVENT 处理）
    spdlog::debug("Handling event frame from connection {}", frame.frame_id);

    std::vector<base::EventMessage> event_list;
    int ret = protocol_transform_->ConvertProToEventMsg(frame, event_list);
    if (ret != 0) {
        spdlog::error("Failed to convert frame to event message");
        return;
    }

    // 处理事件消息
    for (const auto& event_msg : event_list) {
        // 添加到事件队列
        {
            std::lock_guard<std::mutex> lock(event_queue_mutex_);
            event_queue_.push(event_msg);
        }
        event_queue_cv_.notify_one();
    }
}


void ProtocolGateway::AddServiceResponseToMultiFrame(const base::CommonMessage& result_msg) {
    spdlog::debug("Gateway received response from Service: invoke_id={}, b_lastmsg={}",
                 result_msg.invoke_id, result_msg.b_lastmsg);

    std::lock_guard<std::mutex> lock(multi_frame_requests_mutex_);

    // 根据 invoke_id 查找对应的请求
    for (auto& [request_key, request] : multi_frame_requests_) {
        if (request.sent_invoke_id == result_msg.invoke_id) {
            // 找到对应的请求，添加响应
            request.nx_response_list.push_back(result_msg);

            spdlog::debug("Added response to request: key={}, invoke_id={}, total_responses={}, b_lastmsg={}",
                         request_key, result_msg.invoke_id, request.nx_response_list.size(), result_msg.b_lastmsg);

            // 如果这是单帧请求（非控制命令）且收到最后响应，直接发送
            if (!request.is_ctrl_cmd && result_msg.b_lastmsg) {
                spdlog::debug("Processing single-frame response immediately: invoke_id={}", result_msg.invoke_id);

                // 直接发送响应给客户端
                if (send_callback_ && !result_msg.data.empty()) {
                    uint32_t conn_id = request.conn_id;

                    // 检查数据是否是有效的IEC103消息
                    base::Message test_msg;
                    size_t parsed = test_msg.deserialize(result_msg.data);

                    if (parsed > 0) {
                        // 是有效的IEC103消息，直接发送
                        send_callback_(conn_id, result_msg.data);
                        spdlog::debug("Sent single-frame response to connection {}: {} bytes", conn_id, result_msg.data.size());

                        // 显示发送的消息详情
                        spdlog::debug("Response message: TYP={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                                     test_msg.getTyp(), test_msg.getCot(), test_msg.getSource(), test_msg.getTarget(),
                                     test_msg.getFun(), test_msg.getInf());
                    } else {
                        // 不是IEC103消息，直接发送原始数据
                        send_callback_(conn_id, result_msg.data);
                        spdlog::debug("Sent raw single-frame response to connection {}: {} bytes", conn_id, result_msg.data.size());
                    }
                }
            }

            return;
        }
    }

    spdlog::warn("No matching request found for response: invoke_id={}", result_msg.invoke_id);
}

void ProtocolGateway::OnServiceEvent(const base::EventMessage& event_msg) {
    // 添加到事件队列
    {
        std::lock_guard<std::mutex> lock(event_queue_mutex_);
        event_queue_.push(event_msg);
        event_queue_cv_.notify_one();
    }
}

void ProtocolGateway::SetSendCallback(std::function<bool(uint32_t, const std::vector<uint8_t>&)> callback) {
    send_callback_ = callback;
}



// 线程方法实现
void ProtocolGateway::ProtocolFrameProcessorLoop() {
    spdlog::info("协议帧处理线程启动 (参考原始 __DoCallAskLoop)");

    while (!should_stop_.load()) {
        base::ProtocolFrame frame;

        // 等待原始协议帧（参考原始 m_ProCmdDeque）
        {
            std::unique_lock<std::mutex> lock(protocol_cmd_queue_mutex_);
            protocol_cmd_queue_cv_.wait(lock, [this] {
                return !protocol_cmd_queue_.empty() || should_stop_.load();
            });

            if (should_stop_.load()) break;

            if (!protocol_cmd_queue_.empty()) {
                frame = protocol_cmd_queue_.front();
                protocol_cmd_queue_.pop();
            } else {
                continue;
            }
        }

        // 使用Transform判断协议帧类型（参考原始 GetCvtTypeByProInf）
        if (!protocol_transform_) {
            spdlog::error("Protocol transform not available");
            continue;
        }

        base::ProtocolConvertType convert_type = protocol_transform_->GetConvertTypeByFrame(frame);

        switch (convert_type) {
            case base::ProtocolConvertType::TO_CALL:
                // 单帧召唤命令，直接转换并放入Command队列
                HandleSingleFrameCommand(frame);
                break;

            case base::ProtocolConvertType::TO_CTRL:
                // 控制命令（可能多帧），加入多帧处理队列
                HandleMultiFrameCommand(frame);
                break;

            case base::ProtocolConvertType::FROM_LOCAL:
                // 本地直接响应，放入本地任务队列
                HandleLocalResponse(frame);
                break;

            case base::ProtocolConvertType::TO_EVENT:
                // 事件帧，直接转换并放入Event队列
                HandleEventFrame(frame);
                break;

            default:
                spdlog::error("Unknown protocol convert type for frame from connection {}", frame.frame_id);
                break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(thread_sleep_ms_));
    }

    spdlog::info("协议帧处理线程停止");
}

void ProtocolGateway::LocalTaskProcessorLoop() {
    spdlog::info("本地任务处理线程启动 (参考原始线程池)");

    while (!should_stop_.load()) {
        base::ProtocolFrame frame;
        
        // 等待本地任务（参考原始线程池）
        {
            std::unique_lock<std::mutex> lock(local_task_queue_mutex_);
            local_task_queue_cv_.wait(lock, [this] {
                return !local_task_queue_.empty() || should_stop_.load();
            });

            if (should_stop_.load()) break;

            if (!local_task_queue_.empty()) {
                frame = local_task_queue_.front();
                local_task_queue_.pop();
            } else {
                continue;
            }
        }

        try {
            // 处理本地响应任务（参考原始 __GetInfoFromLocal）
            spdlog::debug("Processing local task for frame from connection {}", frame.frame_id);

            // 这里可以直接生成响应，不需要转发给Service
            // 暂时简单实现：发送确认帧
            if (send_callback_) {
                // 创建简单的确认响应
                std::vector<uint8_t> response_data = {0x68, 0x04, 0x07, 0x00, 0x00, 0x00}; // 简单的确认帧
                send_callback_(frame.frame_id, response_data);
                spdlog::debug("Sent local response for connection {}", frame.frame_id);
            }

        } catch (const std::exception& e) {
            spdlog::error("Exception in local task thread: {}", e.what());
        }
    }
    
    spdlog::info("本地任务处理线程停止");
}

void ProtocolGateway::MultiFrameMatcherLoop() {
    spdlog::info("多帧匹配线程启动 (参考原始 __DoCallResponseLoop)");

    while (!should_stop_.load()) {
        try {
            // 处理多帧请求（参考原始 __CallResponseHandle）
            {
                std::lock_guard<std::mutex> lock(multi_frame_requests_mutex_);

                auto it = multi_frame_requests_.begin();
                while (it != multi_frame_requests_.end()) {
                    MultiFrameRequest& request = it->second;

                    // 检查超时（参考原始超时处理）
                    auto now = std::chrono::system_clock::now();
                    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - request.create_time);

                    if (elapsed.count() > request_timeout_seconds_) {
                        spdlog::warn("Multi-frame request timeout, removing: key={}", it->first);
                        it = multi_frame_requests_.erase(it);
                        continue;
                    }

                    // 控制命令的转换处理：为控制命令、收到结束帧且还没有发送给Service
                    if (request.is_ctrl_cmd && request.received_last_cmd && !request.commands_sent) {
                        if (!ProcessMultiFrameControlCommand(it->first, request)) {
                            // 处理失败，删除该请求
                            it = multi_frame_requests_.erase(it);
                            spdlog::error("Multi-frame control command processing failed, removed request");
                            continue;
                        }
                        request.commands_sent = true;
                    }

                    // 结果的转换处理（检查是否收到所有响应）
                    if (request.commands_sent && CheckAndProcessCompleteResponse(it->first, request)) {
                        // 处理完成，删除该请求
                        it = multi_frame_requests_.erase(it);
                        continue;
                    }

                    ++it;
                }
            }

            // TODO: 可以在这里添加多帧请求的超时检查

            std::this_thread::sleep_for(std::chrono::milliseconds(thread_sleep_ms_));

        } catch (const std::exception& e) {
            spdlog::error("Exception in multi-frame request matcher: {}", e.what());
        }
    }

    spdlog::info("多帧匹配线程停止");
}

void ProtocolGateway::EventProcessorLoop() {
    spdlog::info("事件处理线程启动");

    while (!should_stop_.load()) {
        base::EventMessage event_message;

        // 等待事件
        {
            std::unique_lock<std::mutex> lock(event_queue_mutex_);
            event_queue_cv_.wait(lock, [this] { return !event_queue_.empty() || should_stop_.load(); });

            if (should_stop_.load()) break;

            if (!event_queue_.empty()) {
                event_message = event_queue_.front();
                event_queue_.pop();
            } else {
                continue;
            }
        }

        try {
            // 转换事件为协议字节流并发送
            std::vector<uint8_t> protocol_event = ConvertEventToProtocolBytes(event_message);

            if (send_callback_) {
                uint32_t conn_id = static_cast<uint32_t>(event_message.source_id);
                bool result = send_callback_(conn_id, protocol_event);

                if (result) {
                    spdlog::debug("Sent event to network, type: {}", event_message.event_type);
                } else {
                    spdlog::error("Failed to send event to network, type: {}", event_message.event_type);
                }
            }

        } catch (const std::exception& e) {
            spdlog::error("Exception in event processor: {}", e.what());
        }
    }

    spdlog::info("事件处理线程停止");
}







std::vector<uint8_t> ProtocolGateway::ConvertEventToProtocolBytes(const base::EventMessage& event) {
    std::string protocol_str = "EVENT:type=" + std::to_string(event.event_type) + ";desc=" + event.description + ";";
    return std::vector<uint8_t>(protocol_str.begin(), protocol_str.end());
}

// 组件创建方法
bool ProtocolGateway::CreateProtocolTransform() {
    transform::ProtocolTransformFactory::ProtocolType type;

    if (protocol_type_ == "gw104") {
        type = transform::ProtocolTransformFactory::ProtocolType::gw104;
    } else {
        spdlog::error("Unsupported protocol type: {}", protocol_type_);
        return false;
    }

    protocol_transform_ = transform::ProtocolTransformFactory::CreateTransform(type);
    if (!protocol_transform_) {
        spdlog::error("Failed to create transform for protocol: {}", protocol_type_);
        return false;
    }

    spdlog::info("Created protocol transform: {}", protocol_type_);
    return true;
}

bool ProtocolGateway::CreateProtocolService() {
    // 创建Service，使用标准固定ID
    protocol_service_ = std::make_unique<service::ProtocolService>(mediator_);
    if (!protocol_service_) {
        spdlog::error("Failed to create protocol service");
        return false;
    }

    // 将Transform注入到Service中
    // 注意：这里需要创建Transform的副本，因为Gateway也需要使用Transform
    auto service_transform = transform::ProtocolTransformFactory::CreateTransform(
        transform::ProtocolTransformFactory::ProtocolType::gw104);
    protocol_service_->SetProtocolTransform(std::move(service_transform));

    // 初始化并启动Service
    if (!protocol_service_->Initialize()) {
        spdlog::error("Failed to initialize protocol service");
        return false;
    }

    if (!protocol_service_->Start()) {
        spdlog::error("Failed to start protocol service");
        return false;
    }

    spdlog::info("Created and started protocol service");
    return true;
}

// 协议帧解析方法
bool ProtocolGateway::ParseProtocolFrame(const std::vector<uint8_t>& data, base::ProtocolFrame& frame) {
    if (data.empty()) {
        return false;
    }

    // 只使用 Message 反序列化，失败就丢弃
    base::Message msg;
    size_t parsed = msg.deserialize(data);

    if (parsed > 0) {
        // 成功解析为协议消息
        frame.data = data;
        frame.timestamp = std::chrono::system_clock::now();
        frame.type = msg.getTyp();
        frame.cot = msg.getCot();
        frame.asdu_addr = msg.getTarget();  // 使用目标地址作为ASDU地址
        frame.vsq = msg.getVsq();
        frame.is_last_frame = IsLastFrameFromVSQ(msg.getVsq(), msg.getTyp());  // 从VSQ正确解析
        frame.frame_id = 0;  // 这里会在 OnNetworkProtocolData 中设置为实际的连接ID

        spdlog::debug("Parsed protocol frame: TYP={:02X}, VSQ={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}, data_len={}",
                     msg.getTyp(), msg.getVsq(), msg.getCot(), msg.getSource(), msg.getTarget(), msg.getFun(), msg.getInf(), data.size());

        return true;
    } else {
        // 解析失败，丢弃数据
        spdlog::warn("Failed to parse protocol frame, discarding data of length {}", data.size());
        return false;
    }
}

bool ProtocolGateway::IsLastFrame(const base::ProtocolFrame& frame) {
    return frame.is_last_frame;
}

bool ProtocolGateway::IsLastFrameFromVSQ(uint8_t vsq, uint8_t typ) {
    // 解析VSQ字段：CP8{数目, SQ}
    uint8_t sq = (vsq & 0x80) >> 7;  // 提取SQ位（第8位）
    uint8_t num = vsq & 0x7F;        // 提取数目字段（第1-7位）

    spdlog::debug("VSQ analysis: vsq={:02X}, sq={}, num={}, typ={:02X}", vsq, sq, num, typ);

    // 根据协议类型和VSQ判断
    if (sq == 1) {
        // SQ=1：单个信息元素，通常是单帧
        spdlog::debug("SQ=1: Single information element, treating as last frame");
        return true;
    } else {
        // SQ=0：顺序寻址，需要根据具体情况判断
        if (num == 1) {
            // 只有1个信息元素，是最后一帧
            spdlog::debug("SQ=0, num=1: Single element sequence, treating as last frame");
            return true;
        } else if (num > 1) {
            // 多个信息元素，根据协议类型判断
            // 对于测试：Type 1 = 单帧，Type 2 = 多帧
            if (typ == 1) {
                spdlog::debug("Type 1: Single frame test, treating as last frame");
                return true;
            } else if (typ == 2) {
                spdlog::debug("Type 2: Multi-frame test, treating as NOT last frame");
                return false;  // 多帧测试，不是最后一帧
            } else {
                // 其他类型，根据数目判断（简化处理）
                spdlog::debug("Other type: num={}, treating as last frame", num);
                return true;
            }
        } else {
            // num == 0，无效
            spdlog::warn("Invalid VSQ: num=0, treating as last frame");
            return true;
        }
    }
}



// 多帧处理辅助方法实现

bool ProtocolGateway::AddFrameToMultiFrameRequest(const base::ProtocolFrame& frame, uint32_t conn_id) {
    std::lock_guard<std::mutex> lock(multi_frame_requests_mutex_);

    std::string request_key = GenerateMultiFrameRequestKey(frame, conn_id);

    // 查找是否已存在相关的多帧请求
    auto it = multi_frame_requests_.find(request_key);
    if (it != multi_frame_requests_.end()) {
        // 检查是否为后续帧
        bool is_follow_up = false;
        for (const auto& existing_frame : it->second.frame_list) {
            if (protocol_transform_->IsFollowUpFrame(existing_frame, frame)) {
                is_follow_up = true;
                break;
            }
        }

        if (is_follow_up) {
            // 添加到现有请求
            it->second.frame_list.push_back(frame);
            it->second.received_last_cmd = frame.is_last_frame;

            spdlog::debug("Added follow-up frame to multi-frame request: key={}", request_key);

            // 检查是否收齐所有帧 - 这里不需要立即处理，让 MultiFrameMatcherLoop 处理
            // 多帧匹配线程会检查 received_last_cmd 并调用 ProcessMultiFrameControlCommand
            return true;
        }

        // 检查是否为预校与执行命令的匹配
        bool is_updated = false;
        for (auto& existing_frame : it->second.frame_list) {
            if (protocol_transform_->UpdateFrameBySrcFrame(existing_frame, const_cast<base::ProtocolFrame&>(frame))) {
                is_updated = true;
                // 更新最后一帧标识
                if (frame.is_last_frame) {
                    it->second.received_last_cmd = true;
                }
                spdlog::debug("Updated frame by src frame in multi-frame request: key={}", request_key);
                break;
            }
        }

        if (is_updated) {
            return true;
        }
    }

    // 创建新的多帧请求
    MultiFrameRequest new_request;
    new_request.frame_list.push_back(frame);
    new_request.is_ctrl_cmd = true;
    new_request.received_last_cmd = frame.is_last_frame;
    new_request.conn_id = conn_id;

    multi_frame_requests_[request_key] = new_request;

    spdlog::debug("Created new multi-frame request: key={}", request_key);

    // 如果是单帧控制命令，让 MultiFrameMatcherLoop 处理
    // 多帧匹配线程会检查 received_last_cmd 并调用 ProcessMultiFrameControlCommand

    return true;
}



std::string ProtocolGateway::GenerateMultiFrameRequestKey(const base::ProtocolFrame& frame, uint32_t conn_id) {
    // 生成多帧请求的唯一键（参考原始匹配逻辑）
    return std::to_string(conn_id) + "_" +
           std::to_string(frame.type) + "_" +
           std::to_string(frame.asdu_addr) + "_" +
           std::to_string(frame.addr) + "_" +
           std::to_string(frame.cpu) + "_" +
           std::to_string(frame.zone) + "_" +
           std::to_string(frame.cot) + "_" +
           std::to_string(frame.fun);
}



// RequestMatcherThreadLoop 中使用的方法实现

bool ProtocolGateway::ProcessMultiFrameControlCommand(const std::string& request_key, MultiFrameRequest& request) {
    // 参考原始 __PackegCtrlCmdHandle
    spdlog::debug("Processing multi-frame control command: key={}", request_key);

    std::vector<base::CommonMessage> common_list;
    base::ProtocolFrameList result_frames;

    int ret = protocol_transform_->ConvertProToCommonMsg(request.frame_list, common_list, result_frames);
    if (ret != 0) {
        spdlog::error("Failed to convert multi-frame control command");

        // 发送错误回应
        for (const auto& result_frame : result_frames) {
            if (send_callback_) {
                send_callback_(request.conn_id, result_frame.data);
            }
        }
        return false;
    }

    // 转换成功，发送给Service
    // 注意：我们假设只有一个CommonMessage（多帧合并为一个命令）
    if (common_list.size() != 1) {
        spdlog::error("Expected exactly 1 CommonMessage, got {}", common_list.size());
        return false;
    }

    auto& common_msg = common_list[0];
    common_msg.source_id = request.conn_id;
    common_msg.target_id = base::SERVICE_SUBJECT_ID;

    // 自己生成invoke_id，确保匹配安全
    std::string invoke_id = utils::invoke_id::Generate(base::GATEWAY_OBSERVER_ID);
    common_msg.invoke_id = invoke_id;

    // 发送给Service
    if (observer_) {
        auto result = observer_->SendCommand(common_msg, base::SERVICE_SUBJECT_ID);
        if (result) {
            // 记录我们生成的 invoke_id
            request.sent_invoke_id = invoke_id;
            spdlog::debug("Sent multi-frame control command to Service: invoke_id={}", invoke_id);
        } else {
            spdlog::error("Failed to send multi-frame control command to Service");
            return false;
        }
    } else {
        spdlog::error("Observer not available");
        return false;
    }

    return true;
}

bool ProtocolGateway::CheckAndProcessCompleteResponse(const std::string& request_key, MultiFrameRequest& request) {
    // 简化的响应检查逻辑
    if (request.nx_response_list.empty()) {
        return false; // 没有收到任何响应
    }

    // 检查最后一个响应是否为最后一帧
    bool last_frame_received = request.nx_response_list.back().b_lastmsg;

    if (last_frame_received) {
        spdlog::debug("All responses received for multi-frame request: key={}, total_responses={}",
                     request_key, request.nx_response_list.size());

        // 转换响应并发送
        ProcessCompleteMultiFrameResponse(request_key, request);
        return true;
    }

    return false;
}

void ProtocolGateway::ProcessCompleteMultiFrameResponse(const std::string& request_key, MultiFrameRequest& request) {
    spdlog::debug("Processing complete multi-frame response: key={}, responses={}",
                 request_key, request.nx_response_list.size());

    // 按顺序发送所有响应帧
    for (const auto& response : request.nx_response_list) {
        if (send_callback_ && !response.data.empty()) {
            uint32_t conn_id = static_cast<uint32_t>(response.target_id);

            // 检查数据是否是有效的IEC103消息
            base::Message test_msg;
            size_t parsed = test_msg.deserialize(response.data);

            if (parsed > 0) {
                // 是有效的IEC103消息，直接发送
                send_callback_(conn_id, response.data);
                spdlog::debug("Sent multi-frame response to connection {}: {} bytes, b_lastmsg={}",
                             conn_id, response.data.size(), response.b_lastmsg);

                // 显示发送的消息详情
                spdlog::debug("Response message: TYP={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                             test_msg.getTyp(), test_msg.getCot(), test_msg.getSource(), test_msg.getTarget(),
                             test_msg.getFun(), test_msg.getInf());
            } else {
                // 不是IEC103消息，直接发送原始数据
                send_callback_(conn_id, response.data);
                spdlog::debug("Sent raw multi-frame response to connection {}: {} bytes", conn_id, response.data.size());
            }
        }
    }

    spdlog::info("Completed multi-frame response processing: key={}, sent {} frames",
                request_key, request.nx_response_list.size());
}

} // namespace gateway
} // namespace protocol
} // namespace zexuan
