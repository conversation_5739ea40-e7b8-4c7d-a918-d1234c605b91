#include "spdlog/spdlog.h"
#include "spdlog/sinks/basic_file_sink.h"
#include <iostream>
int main() 
{
    // 初始化 "net" logger
    try {
        auto net_logger = spdlog::basic_logger_mt("net", "logs/net.log");
        net_logger->set_level(spdlog::level::debug);
        net_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v");
    } catch (const spdlog::spdlog_ex& ex) {
        std::cerr << "Log initialization failed: " << ex.what() << std::endl;
        return 1;
    }
    
    // 现在可以正常使用 spdlog::get("net") 了
    auto logger = spdlog::get("net");
    if (logger) {
        logger->error("TcpConnection::sendInLoop");
        logger->info("Logger initialized successfully!");
    }
    
    // spdlog::info("Welcome to spdlog!");
    // spdlog::error("Some error message with arg: {}", 1);
    
    // spdlog::warn("Easy padding in numbers like {:08d}", 12);
    // spdlog::critical("Support for int: {0:d};  hex: {0:x};  oct: {0:o}; bin: {0:b}", 42);
    // spdlog::info("Support for floats {:03.2f}", 1.23456);
    // spdlog::info("Positional args are {1} {0}..", "too", "supported");
    // spdlog::info("{:<30}", "left aligned");
    
    // spdlog::set_level(spdlog::level::debug); // Set *global* log level to debug
    // spdlog::debug("This message should be displayed..");    
    
    // // change log pattern
    // spdlog::set_pattern("[%H:%M:%S %z] [%n] [%^---%L---%$] [thread %t] %v");
    
    // // Compile time log levels
    // // Note that this does not change the current log level, it will only
    // // remove (depending on SPDLOG_ACTIVE_LEVEL) the call on the release code.
    // SPDLOG_TRACE("Some trace message with param {}", 42);
    // SPDLOG_DEBUG("Some debug message");
}
