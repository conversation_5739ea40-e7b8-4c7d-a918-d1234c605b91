﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{785b3236-4ec4-4550-9ba8-43dfcfbe4005}</UniqueIdentifier>
    </Filter>
    <Filter Include="CPP">
      <UniqueIdentifier>{13600195-d89a-41aa-88fc-42f9d2b95ad9}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\..\platform_include\FileOperate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\LogRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\MyDeque.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\ThreadMutualLock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\TimeConvert.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\NXECObject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\EcTemplateFunc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\os_platform_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\os_platform_fun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcLoadProOperationLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcLoadMsgOperationLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\EcProCommonFun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu10.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\plm_dbm\DataRow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcLoad60870FlowLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\nx_common\nx_errno_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu21.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEc60870CvtObj_DI.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu100_DI.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu1_DI.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcDI104SrvProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcIec104ExplainFactory_DI.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEc60870CvtFactory_DI.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcIec104ProExplain_DI.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcYK104SrvProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoadProOperationLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoadMsgOperationLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\EcProCommonFun.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXECObject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcSrvProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoad60870FlowLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu1.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="nx_ec_pro_srv_sddi104_modify_note.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEc60870CvtObj_DI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu1_DI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu100_DI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcIec104ProExplain_DI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcIec104ExplainFactory_DI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ec_pro_srv_sddi104_export.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEc60870CvtFactory_DI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcDI104SrvProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_pro_srv_sddi104.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
  </ItemGroup>
</Project>