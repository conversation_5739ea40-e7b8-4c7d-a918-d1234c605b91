﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="NXEcDB103SrvProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEc60870CvtFactory_DB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEc60870CvtObj_DB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu12_DB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu13_DB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu16_DB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu15_DB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu21_DB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcIec103ExplainFactory_DB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu17_DB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu1_DB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu7_DB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu42_DB.h">
      <Filter>Source Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ec_pro_srv_db103_export.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ec_srv_db103_modify_note.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEc60870CvtFactory_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEc60870CvtObj_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcDB103SrvProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXECObject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu103.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu102.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu101.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu42.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21_Direct.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu18.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu17.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu16.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu15.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu13.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu12.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu10.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu7.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu6.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu4.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu1.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\EcProCommonFun.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoad60870FlowLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoadMsgOperationLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoadProOperationLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcSrvProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu12_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu13_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu16_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu15_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu21_Direct_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec103ProExplain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec103ExplainFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcIec103ExplainFactory_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu17_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu1_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu7_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu42_DB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_pro_srv_db103.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>