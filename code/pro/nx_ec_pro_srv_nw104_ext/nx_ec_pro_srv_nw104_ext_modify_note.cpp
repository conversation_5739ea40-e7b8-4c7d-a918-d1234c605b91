﻿/*********************************************************************
*ec_srv_nw104_ext_modify_note.cpp       creater:sl     create date:09/03/2015
*-------------------------------------------------------------
*               note: 南网103扩展（2015）服务端规约转换模块历史修改记录
*  
*********************************************************************/

/** Ver 1.0.9  2023-07-03 修改人: 蒋磊
1、修改内容:
（1）新增南网103规约16版召唤开关量支持总招方式和通用分类方式。
     1.数据库获取开关量情景，通用分类ASDU21派生类中添加处理召唤开关量
	 2.装置获取开关量情景，通过配置文件设置NX应答报文是通用分类还是总招方式转成103报文，若读取配置项失败则从装置获取开关量情景下通过通用分类方式应答。
（2）配置文件ecpro.ini新增配置项 
		[NW104EXT]
		//开关量召唤方式: 0-总召方式,1-通用分类方式.
		DI_CALL_TYPE=1;
 2、影响范围：
（1）16版召唤开关量功能；
（2）配置文件ecpro.ini
 3、修改代码范围：
**/

/** Ver 1.0.8.1  2022-02-03 修改人：杨轶森
1、修改内容:
（1）调整1.0.7.0版本上传makefile中版本号拼写错误的问题
 2、影响范围：
（1）无其他影响；
 3、修改代码范围：
**/

/** Ver 1.0.8  2022-02-01 修改人：杨轶森
1、修改内容:
（1）针对基类61870公共中v1.0.8版本中的修改。
	将南网103规约16版召唤历史信息类型转换类进行重载以满足规范中的定义：
	非细分状态量类型	取值
	开关量				0
	保护动作信息		1
	装置告警信息		3

 2、影响范围：
（1）无其他影响；
 3、修改代码范围：
（1）本次修改了NXEcNW104EXTProAsdu17.cpp、NXEcNW104EXTProAsdu17.h共计2个文件；
**/

/** Ver 1.0.7.0  2022-11-07 修改人：蒋磊
1、修改内容:
（1）兼容智能录波器主子站间报文INF扩展  
	INF<0>：从子站召唤普通录波；
	INF<1>：从保护或录波器召唤普通录波；
	INF<2>：从录波器召唤谐波事件录波；
	INF<3>：从录波器召唤谐波周期录波。	
 2、影响范围：
（1）召唤录波文件列表，召唤录波文件，通用服务101和103
 3、修改代码范围：
（1）本次修改了NXEcNW104EXT60870CvtObj.cpp、NXEcNW104EXTProAsdu13.cpp 共计2个文件；
**/

/* 
--------------------------------------------------------------------
* 日    期: 2015-03-09
* 修 改 人: sl     
* 版    本: 1.0.0
* 修改原因: 开始开发
* 修改内容: 
---------------------------------------------------------------------

* 日    期: 2015-08-29
* 修 改 人: sl     
* 版    本: 1.0.1
* 修改原因: 
* 修改内容: 处理检修态信息上送时的传输原因,由0x01变为0x07.
---------------------------------------------------------------------

*/  
/**	Ver1.0.2 2020-04-30	修改人:杨轶森
修改内容:
	增加类NXEcNW104EXTProAsdu105、NXEcNW104EXTProAsdu107、NXEcFileCommon以满足南网103下装文件功能的实现。
-----
----- 发布说明:
----- 增加南网103下装文件功能的实现。
----- 
*/
/**	Ver1.0.3 2020-08-19	修改人:杨轶森
修改内容:
	增加动作、告警、变位等装置上送信息，若为检修态，则过滤不上送主站。涉及修改：TNXEcNW104EXTProAsdu1::FormatAsdu1Body，TNXEcNW104EXTProAsdu2::FormatAsdu1Body，增加类NXEcNW104EXTProAsdu10
-----
----- 发布说明:
----- 增加动作、告警、变位等装置上送信息，若为检修态，则过滤不上送主站。
----- 
*/ 
/**	Ver1.0.4 2020-08-19	修改人:杨轶森
修改内容:
	增加南网复归命令日志输出。涉及修改：TNXEcNW104EXTProAsdu20::MakeCommonMsg_TripRest(）
-----
----- 发布说明:
----- 增加南网复归命令日志输出。
----- 
*/ 
/**  Ver 1.0.5 2021-02-05 修改人：杨轶森
1、修改内容:
（1）增加对复归命令回复结果的处理.
 2、影响范围：
 （1）无;
  3、修改代码范围：
  （1）本次修改了NXEcNW104EXT60870CvtObj.cpp共计1个文件；
  **/

/**  Ver ******* 2021-12-17 修改人：孟俊如
1、修改内容:
（1）在控制功能的预校、执行中，增加相应的闭锁、解锁功能
（2）去除了冗余的“检修态”代码和判断
 2、影响范围：
（1）在南网103中增加了控制功能，具体为：收到预校命令的时候闭锁，收到NX消息的执行回复命令解锁
	 在103中不去按时间刷新闭锁字段，通过61850对上通信中的线程来保障按闭锁配置时间来解锁的功能
（2）去除“检修态”的判断和代码，这一部分在别处已经实现，不需要在对上通信中重复实现
  3、修改代码范围：
  （1）本次修改了NXEcNW104EXTProAsdu2.cpp、NXEcNW104EXTProAsdu1.cpp、NXEcNW104EXTProAsdu10.cpp、NXEcNW104EXTProAsdu10.h、NXEcNW104EXT60870CvtObj.cpp、NXEcNW104EXT60870CvtObj.h共计6个文件；
  **/

/**  Ver 1.0.5.2 2022-1-13 修改人：孟俊如
1、修改内容:
（1）在控制功能的预校、执行中，增加闭锁时间达到时的解锁功能
 2、影响范围：
（1）增加了根据闭锁类型，按时解锁的机制，通过接收到命令读数据库的时候来触发解锁
  3、修改代码范围：
  （1）本次修改了NXEcNW104EXTProAsdu10.cpp、NXEcNW104EXTProAsdu10.h共计2个文件；
  **/

/** Ver 1.0.6  2022-03-20 修改人：杨轶森
1、修改内容:
（1）增加远控厂站闭锁模式：数据库nx_t_cfg_param表中STN_CTRL_LOCK的字段值
	 0-厂站闭锁 1-单装置闭锁（0：收到任意控制命令，厂站下所有保护所有控制权限都闭锁180S。
	 1：收到某装置控制命令，在命令执行完成或者超时时间之内，改装置控制权限闭锁响应时间，站内其他装置不受影响）
（2）增加通过通用分类服务召唤开关量的功能；
（3）对召唤历史功能的调整：原来召唤全部历史信息时，是将动作、告警、状态量等信息分批多个报文下发，现在调整为如果收到主站召唤全部类型历史信息
	 则通过一个报文直接下发召唤；
（4）去除主动上送动作告警等信息时，对检修信息的过滤判断。（此功能在通信框架中已经实现，无需规约额外处理）。

 2、影响范围：
（1）应控制型子站的测试要求，对闭锁机制、召唤开关量、召唤历史、主动上送功能调整；
 3、修改代码范围：
（1）本次修改了NXEcNW104EXTProAsdu10.cpp、NXEcNW104EXTProAsdu10.h、NXEcNW104EXTProAsdu21_Direct.cpp、NXEcNW104EXTProAsdu21.h、NXEcNW104EXTProAsdu17.cpp
NXEcNW104EXT60870CvtObj.cpp、NXEcNW104EXT60870CvtObj.h、NXEcNW104EXTProAsdu1.cpp、NXEcNW104EXTProAsdu2.cpp共计9个文件；
**/


