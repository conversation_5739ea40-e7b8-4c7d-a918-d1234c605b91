﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LocalDebuggerCommand>E:\tsinsource\phoenix\trunk\nx_bin\debug\nx_ec\nx_ec_service.exe</LocalDebuggerCommand>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <LocalDebuggerCommandArguments>-P</LocalDebuggerCommandArguments>
    <LocalDebuggerWorkingDirectory>..\..\..\..\..\nx_bin\debug\nx_ec</LocalDebuggerWorkingDirectory>
  </PropertyGroup>
</Project>