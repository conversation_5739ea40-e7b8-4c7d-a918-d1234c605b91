/**
 * @file message_handler.cpp
 * @brief 消息处理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "../include/message_handler.hpp"
#include "zexuan/base/message_types.hpp"
#include <iomanip>
#include <sstream>

namespace zexuan {
namespace client {

MessageHandler::MessageHandler(uint8_t clientId)
    : clientId_(clientId)
    , logger_(zexuan::Logger::getFileLogger("MessageHandler")) {
    
    logger_->info("MessageHandler created with client ID: {}", static_cast<int>(clientId));
}

MessageHandler::~MessageHandler() {
    logger_->info("MessageHandler destroyed");
}

void MessageHandler::handleRawData(const std::vector<uint8_t>& data) {
    logger_->debug("Handling {} bytes of raw data", data.size());

    // 尝试解析为Message
    zexuan::base::Message receivedMsg;
    size_t parsedBytes = receivedMsg.deserialize(data);

    if (parsedBytes > 0) {
        logger_->info("Successfully parsed message from {} bytes", parsedBytes);
        
        // 构建消息信息字符串
        std::ostringstream oss;
        oss << "\n收到服务器消息:\n";
        oss << "  TYP: " << static_cast<int>(receivedMsg.getTyp()) << "\n";
        oss << "  VSQ: " << static_cast<int>(receivedMsg.getVsq()) << "\n";
        oss << "  COT: " << static_cast<int>(receivedMsg.getCot()) << "\n";
        oss << "  Source: " << static_cast<int>(receivedMsg.getSource()) << "\n";
        oss << "  Target: " << static_cast<int>(receivedMsg.getTarget()) << "\n";
        oss << "  FUN: " << static_cast<int>(receivedMsg.getFun()) << "\n";
        oss << "  INF: " << static_cast<int>(receivedMsg.getInf()) << "\n";
        oss << "  Content: \"" << receivedMsg.getTextContent() << "\"";

        // 通知UI显示消息
        if (uiUpdateCallback_) {
            uiUpdateCallback_(oss.str());
        }

        // 根据消息类型进行处理
        if (receivedMsg.getCot() == static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_RESPONSE)) {
            handleInfoResponse(receivedMsg);
        } else {
            handleRegularMessage(receivedMsg);
        }
    } else {
        logger_->warn("Failed to parse message, displaying as raw data");
        displayRawData(data);
    }
}

std::vector<uint8_t> MessageHandler::createPluginQueryMessage(uint8_t pluginId) {
    // 创建INFO_REQUEST消息
    zexuan::base::Message request;
    request.setTyp(0x01);
    request.setVsq(0x81);
    request.setCot(static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_REQUEST));
    request.setSource(clientId_);
    request.setTarget(pluginId);
    request.setFun(0x00);
    request.setInf(0x01);

    // 序列化消息
    std::vector<uint8_t> data;
    request.serialize(data);
    
    logger_->info("Created plugin query message for plugin ID: {}", static_cast<int>(pluginId));
    return data;
}

std::vector<uint8_t> MessageHandler::createOperationMessage(uint8_t pluginId, 
                                                           size_t operationIndex, 
                                                           const std::string& content) {
    std::lock_guard<std::mutex> lock(pluginInfoMutex_);
    
    auto it = pluginInfoCache_.find(pluginId);
    if (it == pluginInfoCache_.end()) {
        logger_->error("Plugin {} not found in cache", static_cast<int>(pluginId));
        return {};
    }

    try {
        const auto& pluginInfo = it->second;
        const auto& availableMessages = pluginInfo["available_messages"];
        
        if (!availableMessages.is_array() || operationIndex >= availableMessages.size()) {
            logger_->error("Invalid operation index {} for plugin {}", operationIndex, static_cast<int>(pluginId));
            return {};
        }

        const auto& operation = availableMessages[operationIndex];
        const auto& messageTemplate = operation["message_template"];

        // 构造消息
        zexuan::base::Message msg;
        msg.setTyp(static_cast<uint8_t>(messageTemplate.value("typ", 1)));
        msg.setVsq(static_cast<uint8_t>(messageTemplate.value("vsq", 0x81)));
        msg.setCot(static_cast<uint8_t>(messageTemplate.value("cot", 0x06)));
        msg.setSource(clientId_);
        msg.setTarget(pluginId);
        msg.setFun(static_cast<uint8_t>(messageTemplate.value("fun", 0x00)));
        msg.setInf(static_cast<uint8_t>(messageTemplate.value("inf", 0x01)));

        // 设置内容
        if (!content.empty()) {
            msg.setTextContent(content);
        }

        // 序列化消息
        std::vector<uint8_t> data;
        msg.serialize(data);
        
        logger_->info("Created operation message for plugin {}, operation {}", 
                     static_cast<int>(pluginId), operationIndex);
        return data;

    } catch (const std::exception& e) {
        logger_->error("Failed to create operation message: {}", e.what());
        return {};
    }
}

std::vector<uint8_t> MessageHandler::createCustomMessage(uint8_t typ, uint8_t vsq, uint8_t cot,
                                                        uint8_t target, uint8_t fun, uint8_t inf,
                                                        const std::string& content) {
    zexuan::base::Message msg;
    msg.setTyp(typ);
    msg.setVsq(vsq);
    msg.setCot(cot);
    msg.setSource(clientId_);
    msg.setTarget(target);
    msg.setFun(fun);
    msg.setInf(inf);

    if (!content.empty()) {
        msg.setTextContent(content);
    }

    // 序列化消息
    std::vector<uint8_t> data;
    msg.serialize(data);
    
    logger_->info("Created custom message to target {}", static_cast<int>(target));
    return data;
}

nlohmann::json MessageHandler::getPluginInfo(uint8_t pluginId) const {
    std::lock_guard<std::mutex> lock(pluginInfoMutex_);
    auto it = pluginInfoCache_.find(pluginId);
    if (it != pluginInfoCache_.end()) {
        return it->second;
    }
    return nlohmann::json{};
}

bool MessageHandler::isPluginAvailable(uint8_t pluginId) const {
    std::lock_guard<std::mutex> lock(pluginInfoMutex_);
    return pluginInfoCache_.find(pluginId) != pluginInfoCache_.end();
}

void MessageHandler::setUiUpdateCallback(const UiUpdateCallback& callback) {
    uiUpdateCallback_ = callback;
}

void MessageHandler::setMenuUpdateCallback(const MenuUpdateCallback& callback) {
    menuUpdateCallback_ = callback;
}

void MessageHandler::handleInfoResponse(const zexuan::base::Message& message) {
    uint8_t pluginId = message.getSource();
    std::string pluginInfoStr = message.getTextContent();

    logger_->info("Received info response from plugin {}", static_cast<int>(pluginId));

    try {
        // 解析JSON
        nlohmann::json pluginInfo = nlohmann::json::parse(pluginInfoStr);
        
        // 缓存插件信息
        {
            std::lock_guard<std::mutex> lock(pluginInfoMutex_);
            pluginInfoCache_[pluginId] = pluginInfo;
        }

        // 通知UI更新
        std::ostringstream oss;
        oss << "\n✓ 收到插件 " << static_cast<int>(pluginId) << " 的信息";
        if (uiUpdateCallback_) {
            uiUpdateCallback_(oss.str());
        }

        // 通知菜单更新
        if (menuUpdateCallback_) {
            menuUpdateCallback_(pluginId, pluginInfo);
        }

    } catch (const std::exception& e) {
        logger_->error("Failed to parse plugin info JSON: {}", e.what());
        if (uiUpdateCallback_) {
            uiUpdateCallback_("插件信息解析失败");
        }
    }
}

void MessageHandler::handleRegularMessage(const zexuan::base::Message& message) {
    // 处理普通消息，主要是向UI传递信息
    logger_->debug("Handling regular message from plugin {}", static_cast<int>(message.getSource()));
    
    // 如果有特殊处理需求，可以在这里添加
    // 目前只是简单地记录日志，消息内容已经通过handleRawData传递给UI了
}

void MessageHandler::displayRawData(const std::vector<uint8_t>& data) {
    std::ostringstream oss;
    oss << "\n收到原始数据 (" << data.size() << " bytes):\n  ";
    
    for (size_t i = 0; i < data.size() && i < 32; ++i) {
        oss << std::hex << std::setw(2) << std::setfill('0')
            << static_cast<int>(data[i]) << " ";
    }
    
    if (data.size() > 32) {
        oss << "...";
    }
    
    // 通知UI显示原始数据
    if (uiUpdateCallback_) {
        uiUpdateCallback_(oss.str());
    }
}

} // namespace client
} // namespace zexuan
