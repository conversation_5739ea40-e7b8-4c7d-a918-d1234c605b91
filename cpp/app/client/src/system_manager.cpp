/**
 * @file system_manager.cpp
 * @brief 客户端系统管理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "../include/system_manager.hpp"
#include "../include/network_manager.hpp"
#include "../include/ui_manager.hpp"
#include <signal.h>
#include <iostream>
#include <thread>
#include <chrono>
#include <cstdlib>

namespace zexuan {
namespace client {

// 静态成员初始化
SystemManager* SystemManager::instance_ = nullptr;

SystemManager::SystemManager()
    : shouldExit_(false)
    , logger_(zexuan::Logger::getFileLogger("SystemManager")) {
    
    instance_ = this;
    logger_->info("SystemManager created");
}

SystemManager::~SystemManager() {
    gracefulShutdown();
    instance_ = nullptr;
    logger_->info("SystemManager destroyed");
}

void SystemManager::initializeSignalHandling() {
    // 注册信号处理函数
    signal(SIGINT, signalHandler);   // Ctrl+C
    signal(SIGTERM, signalHandler);  // 终止信号
    
    // 忽略SIGPIPE信号（避免写入已关闭的socket导致程序崩溃）
    signal(SIGPIPE, SIG_IGN);
    
    logger_->info("信号处理初始化完成 (支持SIGINT, SIGTERM)");
}

void SystemManager::setComponents(std::shared_ptr<NetworkManager> networkManager,
                                  std::shared_ptr<UiManager> uiManager) {
    networkManager_ = networkManager;
    uiManager_ = uiManager;
    logger_->info("组件引用设置完成");
}

void SystemManager::setShutdownCallback(const ShutdownCallback& callback) {
    shutdownCallback_ = callback;
}

void SystemManager::gracefulShutdown() {
    if (shouldExit_) {
        return; // 已经在退出过程中
    }
    
    shouldExit_ = true;
    logger_->info("开始优雅退出...");
    std::cout << "\n正在优雅退出客户端..." << std::endl;

    try {
        // 1. 停止UI管理器
        if (auto uiManager = uiManager_.lock()) {
            logger_->info("停止UI管理器...");
            uiManager->stop();
            // 给UI管理器一些时间完成清理
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }

        // 2. 停止网络管理器
        if (auto networkManager = networkManager_.lock()) {
            logger_->info("停止网络管理器...");
            networkManager->stop();
            // 给网络管理器更多时间完成TCP连接清理
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }

        // 3. 调用自定义退出回调
        if (shutdownCallback_) {
            logger_->info("执行自定义退出回调...");
            shutdownCallback_();
        }

        logger_->info("优雅退出完成");
        std::cout << "客户端已优雅退出" << std::endl;
        
    } catch (const std::exception& e) {
        logger_->error("优雅退出过程中发生异常: {}", e.what());
        std::cout << "退出过程中发生异常: " << e.what() << std::endl;
    }
}

void SystemManager::signalHandler(int sig) {
    if (instance_) {
        auto logger = zexuan::Logger::getFileLogger("SystemManager");
        const char* sigName = (sig == SIGINT ? "SIGINT" : 
                              sig == SIGTERM ? "SIGTERM" : 
                              "UNKNOWN");
        
        logger->info("收到信号 {} ({})", sig, sigName);
        std::printf("\n收到退出信号 %s，正在优雅退出...\n", sigName);
        
        // 设置信号处理为默认，防止重复信号
        signal(sig, SIG_DFL);
        
        // 异步执行优雅退出，避免在信号处理器中执行复杂操作
        std::thread([sig]() {
            try {
                if (instance_) {
                    instance_->gracefulShutdown();
                }
                
                // 给系统足够时间完成所有清理工作
                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
                
                // 正常退出
                exit(0);
                
            } catch (const std::exception& e) {
                std::printf("优雅退出过程中发生异常: %s\n", e.what());
                exit(1);
            }
        }).detach();
    } else {
        // 如果没有实例，直接退出
        std::printf("\n收到退出信号，立即退出\n");
        exit(sig == SIGINT ? 0 : 1);
    }
}

} // namespace client
} // namespace zexuan
