/**
 * @file network_manager.cpp
 * @brief 网络管理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "../include/network_manager.hpp"
#include <cstring>
#include <chrono>

namespace zexuan {
namespace client {

NetworkManager::NetworkManager(const std::string& serverHost, uint16_t serverPort)
    : serverAddr_(serverHost, serverPort)
    , connected_(false)
    , running_(false)
    , logger_(zexuan::Logger::getFileLogger("NetworkManager")) {
    
    logger_->info("NetworkManager created - Server: {}:{}", serverHost, serverPort);
}

NetworkManager::~NetworkManager() {
    stop();
    logger_->info("NetworkManager destroyed");
}

bool NetworkManager::connect() {
    try {
        // 启动事件循环线程
        running_ = true;
        eventLoopThread_ = std::make_unique<std::thread>(&NetworkManager::eventLoopThread, this);

        // 等待EventLoop和Client在事件循环线程中初始化完成
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        // 等待连接建立（简单的轮询方式）
        int timeout = 5000; // 5秒超时
        while (!connected_ && timeout > 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            timeout -= 10;
        }

        if (connected_) {
            logger_->info("Connected to server successfully");
            return true;
        } else {
            logger_->error("Connection timeout");
            return false;
        }

    } catch (const std::exception& e) {
        logger_->error("Connection failed: {}", e.what());
        return false;
    }
}

void NetworkManager::disconnect() {
    if (client_ && connected_) {
        logger_->info("Disconnecting from server...");
        
        // 在事件循环线程中执行断连操作
        if (loop_) {
            loop_->runInLoop([this]() {
                if (client_) {
                    client_->disconnect();
                }
            });
            
            // 等待连接完全断开
            int timeout = 1000; // 1秒超时
            while (connected_ && timeout > 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                timeout -= 10;
            }
        }
        
        connected_ = false;
        logger_->info("Disconnected from server");
    }
}

bool NetworkManager::sendData(const std::vector<uint8_t>& data) {
    if (!connected_ || !connection_) {
        logger_->error("Not connected to server");
        return false;
    }

    try {
        // 发送原始数据
        connection_->send(reinterpret_cast<const char*>(data.data()), data.size());
        logger_->debug("Data sent successfully, size: {} bytes", data.size());
        return true;

    } catch (const std::exception& e) {
        logger_->error("Failed to send data: {}", e.what());
        return false;
    }
}

bool NetworkManager::sendMessage(const zexuan::base::Message& message) {
    try {
        // 序列化消息
        std::vector<uint8_t> data;
        message.serialize(data);
        
        // 发送序列化后的数据
        return sendData(data);

    } catch (const std::exception& e) {
        logger_->error("Failed to serialize and send message: {}", e.what());
        return false;
    }
}

void NetworkManager::setMessageCallback(const MessageCallback& callback) {
    messageCallback_ = callback;
}

void NetworkManager::setConnectionCallback(const ConnectionCallback& callback) {
    connectionCallback_ = callback;
}

void NetworkManager::stop() {
    if (!running_) {
        return; // 已经停止
    }
    
    logger_->info("Stopping NetworkManager...");
    running_ = false;
    
    // 先断开连接
    disconnect();

    if (loop_) {
        // 在事件循环线程中退出循环
        loop_->runInLoop([this]() {
            loop_->quit();
        });
    }

    // 等待事件循环线程结束
    if (eventLoopThread_ && eventLoopThread_->joinable()) {
        logger_->debug("Waiting for event loop thread to finish...");
        eventLoopThread_->join();
        logger_->debug("Event loop thread finished");
    }

    // 清理资源
    client_.reset();
    connection_.reset();
    loop_.reset();

    logger_->info("NetworkManager stopped successfully");
}

void NetworkManager::onConnection(const zexuan::net::TcpConnectionPtr& conn) {
    if (conn->connected()) {
        logger_->info("Connected to {}", conn->peerAddress().toIpPort());
        connection_ = conn;
        connected_ = true;
        
        // 通知连接状态变化
        if (connectionCallback_) {
            connectionCallback_(true);
        }
    } else {
        logger_->info("Disconnected from {}", conn->peerAddress().toIpPort());
        connection_.reset();
        connected_ = false;
        
        // 通知连接状态变化
        if (connectionCallback_) {
            connectionCallback_(false);
        }
    }
}

void NetworkManager::onMessage(const zexuan::net::TcpConnectionPtr& conn,
                              zexuan::net::Buffer* buffer,
                              zexuan::net::Timestamp receiveTime) {
    // 处理接收到的原始数据
    size_t len = buffer->readableBytes();
    if (len > 0) {
        std::vector<uint8_t> data(len);
        memcpy(data.data(), buffer->peek(), len);
        buffer->retrieveAll();

        logger_->debug("Received {} bytes from server", len);

        // 通过回调函数传递给上层
        if (messageCallback_) {
            messageCallback_(data);
        }
    }
}

void NetworkManager::eventLoopThread() {
    logger_->info("Network event loop thread started");

    try {
        // 在事件循环线程中创建EventLoop
        loop_ = std::make_unique<zexuan::net::EventLoop>();

        // 在事件循环线程中创建TCP客户端
        client_ = std::make_unique<zexuan::net::TcpClient>(loop_.get(), serverAddr_, "NetworkManager");

        // 设置回调函数
        client_->setConnectionCallback(
            std::bind(&NetworkManager::onConnection, this, std::placeholders::_1));
        client_->setMessageCallback(
            std::bind(&NetworkManager::onMessage, this, std::placeholders::_1,
                     std::placeholders::_2, std::placeholders::_3));

        // 启动连接
        client_->connect();

        // 运行事件循环
        loop_->loop();

    } catch (const std::exception& e) {
        logger_->error("Network event loop exception: {}", e.what());
    }

    logger_->info("Network event loop thread ended");
}

} // namespace client
} // namespace zexuan
