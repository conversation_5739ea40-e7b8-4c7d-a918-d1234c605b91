/**
 * @file message_handler.hpp
 * @brief 消息处理器 - 负责消息解析和业务逻辑处理
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_CLIENT_MESSAGE_HANDLER_HPP
#define ZEXUAN_CLIENT_MESSAGE_HANDLER_HPP

#include <memory>
#include <functional>
#include <map>
#include <mutex>
#include <atomic>

#include "zexuan/base/message.hpp"
#include "zexuan/logger.hpp"
#include <nlohmann/json.hpp>

namespace zexuan {
namespace client {

// 前向声明
class UiManager;

/**
 * @brief 消息处理器 - 处理消息解析、插件信息管理和UI通信
 */
class MessageHandler {
public:
    // UI更新回调函数类型
    using UiUpdateCallback = std::function<void(const std::string&)>;
    using MenuUpdateCallback = std::function<void(uint8_t, const nlohmann::json&)>;

    /**
     * @brief 构造函数
     * @param clientId 客户端ID
     */
    explicit MessageHandler(uint8_t clientId = 233);

    /**
     * @brief 析构函数
     */
    ~MessageHandler();

    /**
     * @brief 处理接收到的原始数据
     * @param data 原始数据
     */
    void handleRawData(const std::vector<uint8_t>& data);

    /**
     * @brief 创建插件查询消息
     * @param pluginId 插件ID
     * @return 序列化后的消息数据
     */
    std::vector<uint8_t> createPluginQueryMessage(uint8_t pluginId);

    /**
     * @brief 创建操作消息
     * @param pluginId 插件ID
     * @param operationIndex 操作索引
     * @param content 消息内容
     * @return 序列化后的消息数据
     */
    std::vector<uint8_t> createOperationMessage(uint8_t pluginId, 
                                               size_t operationIndex, 
                                               const std::string& content = "");

    /**
     * @brief 创建自定义消息
     * @param typ 类型标识
     * @param vsq 可变结构限定词
     * @param cot 传送原因
     * @param target 目标地址
     * @param fun 功能类型
     * @param inf 信息序号
     * @param content 消息内容
     * @return 序列化后的消息数据
     */
    std::vector<uint8_t> createCustomMessage(uint8_t typ, uint8_t vsq, uint8_t cot,
                                            uint8_t target, uint8_t fun, uint8_t inf,
                                            const std::string& content = "");

    /**
     * @brief 获取插件信息
     * @param pluginId 插件ID
     * @return 插件信息JSON，如果未找到返回空JSON
     */
    nlohmann::json getPluginInfo(uint8_t pluginId) const;

    /**
     * @brief 检查插件是否可用
     * @param pluginId 插件ID
     * @return true 可用，false 不可用
     */
    bool isPluginAvailable(uint8_t pluginId) const;

    /**
     * @brief 设置UI更新回调
     * @param callback 回调函数
     */
    void setUiUpdateCallback(const UiUpdateCallback& callback);

    /**
     * @brief 设置菜单更新回调
     * @param callback 回调函数
     */
    void setMenuUpdateCallback(const MenuUpdateCallback& callback);

    /**
     * @brief 获取客户端ID
     * @return 客户端ID
     */
    uint8_t getClientId() const { return clientId_; }

private:
    /**
     * @brief 处理INFO_RESPONSE消息
     * @param message 解析后的消息
     */
    void handleInfoResponse(const zexuan::base::Message& message);

    /**
     * @brief 处理普通消息
     * @param message 解析后的消息
     */
    void handleRegularMessage(const zexuan::base::Message& message);

    /**
     * @brief 显示原始数据
     * @param data 原始数据
     */
    void displayRawData(const std::vector<uint8_t>& data);

private:
    // 客户端ID
    uint8_t clientId_;

    // 插件信息缓存
    std::map<uint8_t, nlohmann::json> pluginInfoCache_;
    mutable std::mutex pluginInfoMutex_;

    // 回调函数
    UiUpdateCallback uiUpdateCallback_;
    MenuUpdateCallback menuUpdateCallback_;

    // 日志
    std::shared_ptr<spdlog::logger> logger_;
};

} // namespace client
} // namespace zexuan

#endif // ZEXUAN_CLIENT_MESSAGE_HANDLER_HPP
